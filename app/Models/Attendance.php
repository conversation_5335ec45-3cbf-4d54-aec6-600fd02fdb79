<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class Attendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'meeting_date',
        'status',
        'marked_by',
        'marked_at',
        'method',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'meeting_date' => 'date',
            'marked_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Helper methods for validation
    public static function validationRules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'meeting_date' => 'required|date|before_or_equal:today',
            'status' => 'required|in:present,absent,late,excused',
            'method' => 'required|in:manual,qr_code,bulk,auto',
            'notes' => 'nullable|string|max:500',
        ];
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->where('meeting_date', now()->toDateString());
    }

    public function scopePresent($query)
    {
        return $query->where('status', 'present');
    }

    public function scopeAbsent($query)
    {
        return $query->where('status', 'absent');
    }

    public function scopeByDateRange($query, $from, $to)
    {
        return $query->whereBetween('meeting_date', [$from, $to]);
    }

    public function scopeByMethod($query, $method)
    {
        return $query->where('method', $method);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
