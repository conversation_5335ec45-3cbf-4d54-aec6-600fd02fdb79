<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class User extends Model
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'phone',
        'gender',
        'birth_date',
        'address',
        'facebook_url',
        'college',
        'academic_year',
        'department',
        'first_attendance_date',
        'qr_token',
        'is_active',
        'notes',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'qr_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'birth_date' => 'date',
            'first_attendance_date' => 'date',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    // Relationships
    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    // Helper methods for validation

    /**
     * Validation rules for attendance users (students/attendees)
     */
    public static function validationRules(): array
    {
        return [
            'name' => 'required|string|min:2|max:255',
            'phone' => 'required|string|size:11|regex:/^01[0-9]{9}$/',
            'gender' => 'required|in:male,female',
            'birth_date' => 'required|date|before:today',
            'address' => 'nullable|string|min:10|max:500',
            'facebook_url' => 'nullable|url',
            'college' => 'required|string|min:2|max:100',
            'academic_year' => 'required|integer|between:1,4',
            'department' => 'nullable|string|min:2|max:100',
            'is_active' => 'boolean',
        ];
    }

    // Accessors
    public function getAgeAttribute(): int
    {
        return $this->birth_date ? $this->birth_date->age : 0;
    }

    public function getAttendanceRateAttribute(): float
    {
        $total = $this->attendances()->count();
        if ($total === 0) return 0;

        $present = $this->attendances()->where('status', 'present')->count();
        return round(($present / $total) * 100, 2);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByYear($query, $year)
    {
        return $query->where('academic_year', $year);
    }

    public function scopeByCollege($query, $college)
    {
        return $query->where('college', 'like', "%{$college}%");
    }

    public function scopeBirthdayThisMonth($query)
    {
        return $query->whereMonth('birth_date', now()->month);
    }

    /**
     * Users who have QR tokens for attendance scanning
     */
    public function scopeWithQrTokens($query)
    {
        return $query->whereNotNull('qr_token');
    }

    // Methods
    public function generateQrToken(): string
    {
        $this->qr_token = 'ARSANIOS-' . $this->id . '-' . \Illuminate\Support\Str::random(8);
        $this->save();
        return $this->qr_token;
    }

    /**
     * Mark attendance for this user
     */
    public function markAttendance(string $status = 'present', ?string $markedBy = null): Attendance
    {
        $attendance = $this->attendances()->updateOrCreate(
            ['meeting_date' => now()->toDateString()],
            [
                'status' => $status,
                'marked_by' => $markedBy,
                'marked_at' => now(),
                'method' => 'manual'
            ]
        );

        // Update first_attendance_date if this is the user's first attendance
        if ($status === 'present' && is_null($this->first_attendance_date)) {
            $this->update(['first_attendance_date' => now()->toDateString()]);
        }

        return $attendance;
    }
}
