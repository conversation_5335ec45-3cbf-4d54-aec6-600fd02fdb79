<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ImportLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'file_name',
        'file_size',
        'import_type',
        'total_records',
        'successful_records',
        'failed_records',
        'errors',
        'imported_by',
        'status',
        'started_at',
        'completed_at',
    ];

    protected function casts(): array
    {
        return [
            'errors' => 'json',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('import_type', $type);
    }

    // Accessors
    public function getSuccessRateAttribute(): float
    {
        if ($this->total_records === 0) return 0;
        return round(($this->successful_records / $this->total_records) * 100, 2);
    }
}
