<?php

namespace App\Http\Requests\Auth;

use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\QueryException;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ];
    }

    /**
     * Attempt to authenticate the request's credentials.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authenticate(): void
    {
        // Be defensive: if rate limiter storage is unavailable or a prior DB error left a transaction open,
        // don't let that bubble a raw SQL error to the user.
        try {
            $this->ensureIsNotRateLimited();
        } catch (QueryException $e) {
            $this->rollbackOpenTransactions();
            Log::warning('Rate limiter pre-check failed; continuing without throttling', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
            ]);
        }

        try {
            $remember = $this->boolean('remember');

            if (! Auth::attempt($this->only('email', 'password'), $remember)) {
                // Increment attempts, but avoid touching cache if we're inside a DB transaction
                if (DB::transactionLevel() === 0) {
                    try {
                        RateLimiter::hit($this->throttleKey());
                    } catch (QueryException $e) {
                        // Roll back any dangling transactions and log the incident
                        $this->rollbackOpenTransactions();
                        Log::warning('Rate limiter hit failed during login attempt', [
                            'key' => $this->throttleKey(),
                            'error' => $e->getMessage(),
                            'code' => $e->getCode(),
                        ]);
                    } catch (\Throwable $e) {
                        Log::warning('Non-DB error during rate limiter hit', ['error' => $e->getMessage()]);
                    }
                } else {
                    Log::warning('Skipping RateLimiter::hit because a DB transaction is active');
                }

                throw ValidationException::withMessages([
                    'email' => __('auth.failed'),
                ]);
            }

            // Successful auth; clear attempts safely
            if (DB::transactionLevel() === 0) {
                try {
                    RateLimiter::clear($this->throttleKey());
                } catch (QueryException $e) {
                    $this->rollbackOpenTransactions();
                    Log::warning('Rate limiter clear failed after successful login', [
                        'key' => $this->throttleKey(),
                        'error' => $e->getMessage(),
                        'code' => $e->getCode(),
                    ]);
                } catch (\Throwable $e) {
                    Log::warning('Non-DB error during rate limiter clear', ['error' => $e->getMessage()]);
                }
            }
        } catch (QueryException $e) {
            // If any database error occurred (e.g., aborted transaction), ensure a rollback and return a friendly message
            $this->rollbackOpenTransactions();

            throw ValidationException::withMessages([
                'email' => __('auth.failed'),
            ]);
        }
    }

    /**
     * Ensure the login request is not rate limited.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => __('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the rate limiting throttle key for the request.
     */
    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->string('email')).'|'.$this->ip());
    }

    /**
     * Roll back any open database transactions to recover from an aborted state.
     */
    protected function rollbackOpenTransactions(): void
    {
        try {
            while (DB::transactionLevel() > 0) {
                DB::rollBack();
            }
        } catch (\Throwable $e) {
            Log::error('Failed to roll back open transactions during login error handling', [
                'error' => $e->getMessage(),
            ]);
        }
    }
}

