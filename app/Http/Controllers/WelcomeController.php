<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Admin;
use App\Models\User;
use App\Models\Attendance;
use App\Models\Setting;

class WelcomeController extends Controller
{
    public function index()
    {
        try {
            // Get system statistics
            $stats = [
                'admins' => Admin::count(),
                'users' => User::count(),
                'attendances_today' => Attendance::whereDate('meeting_date', today())->count(),
                'total_attendances' => Attendance::count(),
            ];

            // Get organization info from settings
            $orgName = Setting::get('organization_name', 'اجتماع الأنبا أرسانيوس لشباب كليتي علوم وتربية');
            $contactPhone = Setting::get('contact_phone', '01234567890');

            // Decode JSON if needed
            if (is_string($orgName) && json_decode($orgName)) {
                $orgName = json_decode($orgName);
            }
            if (is_string($contactPhone) && json_decode($contactPhone)) {
                $contactPhone = json_decode($contactPhone);
            }

            return view('welcome', compact('stats', 'orgName', 'contactPhone'));
        } catch (\Exception $e) {
            // If there's any database error, show a simple page
            return view('welcome-simple', ['error' => $e->getMessage()]);
        }
    }
}
