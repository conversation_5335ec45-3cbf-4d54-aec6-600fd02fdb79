<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\User;

class UserController extends Controller
{
    /**
     * Get all attendance users (with filtering, sorting, pagination)
     */
    public function index(Request $request): JsonResponse
    {
        // Validate query params
        $validated = $request->validate([
            'year' => 'nullable|integer|between:1,4',
            'search' => 'nullable|string|min:1|max:255',
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'sort_by' => 'nullable|in:name,academic_year,created_at,birth_date,first_attendance_date',
            'sort_dir' => 'nullable|in:asc,desc',
            'is_active' => 'nullable|boolean',
            'department' => 'nullable|string|max:100',
            'college' => 'nullable|string|max:100',
            'gender' => 'nullable|in:male,female',
            'has_attendance' => 'nullable|boolean',
        ]);

        $perPage = (int)($validated['per_page'] ?? 50);
        $sortBy = $validated['sort_by'] ?? 'name';
        $sortDir = $validated['sort_dir'] ?? 'asc';

        // Helper function to parse boolean parameters
        $parseBooleanParam = function($value) {
            if (is_bool($value)) {
                return $value;
            }
            // Handle string values: "1", "0", "true", "false", etc.
            $parsed = filter_var($value, FILTER_VALIDATE_BOOL, FILTER_NULL_ON_FAILURE);
            if ($parsed === null) {
                // If filter_var fails, try manual string comparison
                return in_array(strtolower((string)$value), ['1', 'true', 'yes', 'on']);
            }
            return $parsed;
        };

        // Handle is_active parameter more robustly
        $isActive = true; // default value
        if (array_key_exists('is_active', $validated)) {
            $isActive = $parseBooleanParam($validated['is_active']);
        }

        // Handle has_attendance parameter
        $hasAttendance = null;
        if (array_key_exists('has_attendance', $validated)) {
            $hasAttendance = $parseBooleanParam($validated['has_attendance']);
        }

        // Create cache key for this specific query
        $cacheKey = 'users_query_' . md5(serialize($validated) . $request->get('page', 1));

        // Try to get cached results first (cache for 2 minutes for frequently accessed data)
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult && !$request->has('refresh')) {
            return response()->json($cachedResult);
        }

        // Build optimized query with proper indexing
        $query = User::query()
            ->select([
                'id', 'name', 'phone', 'gender', 'birth_date', 'address', 'facebook_url',
                'college', 'academic_year', 'department', 'first_attendance_date',
                'is_active', 'created_at', 'updated_at'
            ])
            ->when($isActive !== null, fn($q) => $q->where('is_active', $isActive))
            ->when(isset($validated['year']), fn($q) => $q->where('academic_year', $validated['year']))
            ->when(isset($validated['department']) && $validated['department'] !== '',
                fn($q) => $q->where('department', 'ILIKE', '%' . $validated['department'] . '%'))
            ->when(isset($validated['college']) && $validated['college'] !== '',
                fn($q) => $q->where('college', 'ILIKE', '%' . $validated['college'] . '%'))
            ->when(isset($validated['gender']), fn($q) => $q->where('gender', $validated['gender']))
            ->when($hasAttendance !== null, function($q) use ($hasAttendance) {
                if ($hasAttendance) {
                    $q->whereNotNull('first_attendance_date');
                } else {
                    $q->whereNull('first_attendance_date');
                }
            })
            ->when(isset($validated['search']) && $validated['search'] !== '', function ($q) use ($validated) {
                $searchTerm = trim($validated['search']);

                // Use full-text search for PostgreSQL if available, otherwise fallback to LIKE
                if (config('database.default') === 'pgsql' && strlen($searchTerm) > 2) {
                    $q->where(function ($inner) use ($searchTerm) {
                        $inner->whereRaw("to_tsvector('arabic', name) @@ plainto_tsquery('arabic', ?)", [$searchTerm])
                              ->orWhereRaw("to_tsvector('arabic', college) @@ plainto_tsquery('arabic', ?)", [$searchTerm])
                              ->orWhereRaw("to_tsvector('arabic', department) @@ plainto_tsquery('arabic', ?)", [$searchTerm])
                              ->orWhere('phone', 'LIKE', '%' . $searchTerm . '%');
                    });
                } else {
                    // Fallback to LIKE search with proper escaping
                    $term = '%' . str_replace(['%','_'], ['\%','\_'], $searchTerm) . '%';
                    $q->where(function ($inner) use ($term) {
                        $inner->where('name', 'ILIKE', $term)
                              ->orWhere('phone', 'LIKE', $term)
                              ->orWhere('college', 'ILIKE', $term)
                              ->orWhere('department', 'ILIKE', $term);
                    });
                }
            });

        // Apply optimized sorting with proper index usage
        $this->applySorting($query, $sortBy, $sortDir, $validated);

        // Execute query with pagination
        $users = $query->paginate($perPage)->withQueryString();

        // Get enhanced summary with filters applied
        $summary = $this->getUsersSummary($validated, $isActive);

        // Get filter options for frontend
        $filterOptions = $this->getFilterOptions($validated);

        $result = [
            'users' => $users,
            'summary' => $summary,
            'filter_options' => $filterOptions,
            'query_info' => [
                'total_queries' => DB::getQueryLog() ? count(DB::getQueryLog()) : 0,
                'cache_hit' => false,
            ]
        ];

        // Cache the result for 2 minutes
        Cache::put($cacheKey, $result, now()->addMinutes(2));

        return response()->json($result);
    }

    /**
     * Create a new attendance user (student/attendee)
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate(User::validationRules());

        $user = User::create($request->all());
        $user->generateQrToken();

        return response()->json([
            'message' => 'User created successfully',
            'user' => $user,
            'qr_token' => $user->qr_token
        ], 201);
    }

    /**
     * Get user details
     */
    public function show(User $user): JsonResponse
    {
        return response()->json([
            'user' => $user->load('attendances'),
            'attendance_rate' => $user->attendance_rate,
            'age' => $user->age,
        ]);
    }

    /**
     * Update user
     */
    public function update(Request $request, User $user): JsonResponse
    {
        $rules = User::validationRules();
        unset($rules['phone']); // Don't require unique phone on update

        $request->validate($rules);
        $user->update($request->all());

        return response()->json([
            'message' => 'User updated successfully',
            'user' => $user
        ]);
    }

    /**
     * Generate QR code for user
     */
    public function generateQr(User $user): JsonResponse
    {
        $qrToken = $user->generateQrToken();

        return response()->json([
            'message' => 'QR token generated successfully',
            'qr_token' => $qrToken,
            'qr_url' => url("/attendance/qr/{$qrToken}")
        ]);
    }
    /**
     * Delete user (soft delete)
     */
    public function destroy(User $user): JsonResponse
    {
        $user->delete();

        return response()->json([
            'message' => 'User deleted successfully'
        ]);
    }

    /**
     * Bulk operations on users
     */
    public function bulkOperations(Request $request): JsonResponse
    {
        return response()->json([
            'message' => 'Bulk operations - not implemented yet'
        ]);
    }

    /**
     * Search users
     */
    public function search(Request $request): JsonResponse
    {
        return response()->json([
            'message' => 'Search users - not implemented yet'
        ]);
    }

    /**
     * Apply optimized sorting with proper index usage
     */
    private function applySorting($query, string $sortBy, string $sortDir, array $validated)
    {
        // Use composite indexes when possible for better performance
        switch ($sortBy) {
            case 'name':
                if (isset($validated['year'])) {
                    $query->orderBy('academic_year', $sortDir)->orderBy('name', $sortDir);
                } elseif (isset($validated['is_active'])) {
                    $query->orderBy('is_active', $sortDir)->orderBy('name', $sortDir);
                } else {
                    $query->orderBy('name', $sortDir);
                }
                break;

            case 'created_at':
                if (isset($validated['year'])) {
                    $query->orderBy('academic_year', $sortDir)->orderBy('created_at', $sortDir);
                } elseif (isset($validated['is_active'])) {
                    $query->orderBy('is_active', $sortDir)->orderBy('created_at', $sortDir);
                } else {
                    $query->orderBy('created_at', $sortDir);
                }
                break;

            case 'academic_year':
                $query->orderBy('academic_year', $sortDir)->orderBy('name', 'asc');
                break;

            case 'birth_date':
                $query->orderBy('birth_date', $sortDir)->orderBy('name', 'asc');
                break;

            case 'first_attendance_date':
                $query->orderBy('first_attendance_date', $sortDir)->orderBy('name', 'asc');
                break;

            default:
                $query->orderBy('name', 'asc');
        }
    }

    /**
     * Get enhanced summary statistics
     */
    private function getUsersSummary(array $validated, bool $isActive): array
    {
        $cacheKey = 'users_summary_' . md5(serialize($validated)) . '_' . (int)$isActive;

        return Cache::remember($cacheKey, now()->addMinutes(10), function () use ($validated, $isActive) {
            $baseQuery = User::query();

            // Apply same filters as main query for accurate summary
            if ($isActive !== null) {
                $baseQuery->where('is_active', $isActive);
            }
            if (isset($validated['year'])) {
                $baseQuery->where('academic_year', $validated['year']);
            }
            if (isset($validated['department']) && $validated['department'] !== '') {
                $baseQuery->where('department', 'ILIKE', '%' . $validated['department'] . '%');
            }
            if (isset($validated['college']) && $validated['college'] !== '') {
                $baseQuery->where('college', 'ILIKE', '%' . $validated['college'] . '%');
            }
            if (isset($validated['gender'])) {
                $baseQuery->where('gender', $validated['gender']);
            }

            return [
                'total_users' => User::count(),
                'active_users' => User::where('is_active', true)->count(),
                'filtered_count' => $baseQuery->count(),
                'users_with_qr' => User::withQrTokens()->count(),
                'by_year' => User::where('is_active', true)
                    ->groupBy('academic_year')
                    ->selectRaw('academic_year, count(*) as count')
                    ->pluck('count', 'academic_year')
                    ->toArray(),
                'by_gender' => $baseQuery->clone()
                    ->groupBy('gender')
                    ->selectRaw('gender, count(*) as count')
                    ->pluck('count', 'gender')
                    ->toArray(),
            ];
        });
    }

    /**
     * Get filter options for frontend dropdowns
     */
    private function getFilterOptions(array $validated): array
    {
        $cacheKey = 'filter_options_' . md5(serialize($validated));

        return Cache::remember($cacheKey, now()->addMinutes(30), function () use ($validated) {
            $baseQuery = User::where('is_active', true);

            // Apply year filter if specified
            if (isset($validated['year'])) {
                $baseQuery->where('academic_year', $validated['year']);
            }

            return [
                'departments' => $baseQuery->clone()
                    ->whereNotNull('department')
                    ->distinct()
                    ->orderBy('department')
                    ->pluck('department')
                    ->filter()
                    ->values()
                    ->toArray(),

                'colleges' => $baseQuery->clone()
                    ->whereNotNull('college')
                    ->distinct()
                    ->orderBy('college')
                    ->pluck('college')
                    ->filter()
                    ->values()
                    ->toArray(),

                'years' => [1, 2, 3, 4],
                'genders' => ['male', 'female'],
            ];
        });
    }
}
