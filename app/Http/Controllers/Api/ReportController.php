<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ReportController extends Controller
{
    public function weekly(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Weekly report - not implemented yet']);
    }

    public function monthly(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Monthly report - not implemented yet']);
    }

    public function yearly(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Yearly report - not implemented yet']);
    }

    public function custom(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Custom report - not implemented yet']);
    }

    public function export(Request $request, $format): JsonResponse
    {
        return response()->json(['message' => 'Export report - not implemented yet']);
    }
}
