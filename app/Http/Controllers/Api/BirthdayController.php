<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class BirthdayController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Birthday index - not implemented yet']);
    }

    public function upcoming(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Upcoming birthdays - not implemented yet']);
    }

    public function sendGreetings(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Send greetings - not implemented yet']);
    }

    public function calendar(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Birthday calendar - not implemented yet']);
    }
}
