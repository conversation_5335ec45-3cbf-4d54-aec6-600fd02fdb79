<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SettingController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Settings index - not implemented yet']);
    }

    public function update(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Update settings - not implemented yet']);
    }

    public function backup(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Backup settings - not implemented yet']);
    }

    public function restore(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Restore settings - not implemented yet']);
    }
}
