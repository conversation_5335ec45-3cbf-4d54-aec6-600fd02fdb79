<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\Admin;

class AdminController extends Controller
{
    /**
     * Get all admins
     */
    public function index(): JsonResponse
    {
        $admins = Admin::active()->get();

        return response()->json([
            'admins' => $admins,
            'summary' => [
                'total_admins' => Admin::count(),
                'active_admins' => Admin::active()->count(),
                'super_admins' => Admin::byRole('super_admin')->count(),
                'regular_admins' => Admin::byRole('admin')->count(),
                'receptionists' => Admin::byRole('receptionist')->count(),
            ]
        ]);
    }

    /**
     * Create a new admin
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate(Admin::validationRules());

        $admin = Admin::create($request->all());

        return response()->json([
            'message' => 'Admin created successfully',
            'admin' => $admin
        ], 201);
    }

    /**
     * Get admin details
     */
    public function show(Admin $admin): JsonResponse
    {
        return response()->json([
            'admin' => $admin,
            'permissions' => [
                'can_manage_users' => $admin->canManageUsers(),
                'can_mark_attendance' => $admin->canMarkAttendance(),
                'can_view_reports' => $admin->canViewReports(),
                'can_manage_settings' => $admin->canManageSettings(),
            ]
        ]);
    }

    /**
     * Update admin
     */
    public function update(Request $request, Admin $admin): JsonResponse
    {
        $rules = Admin::validationRules();
        unset($rules['email']); // Don't require unique email on update
        unset($rules['password']); // Don't require password on update

        $request->validate($rules);
        $admin->update($request->all());

        return response()->json([
            'message' => 'Admin updated successfully',
            'admin' => $admin
        ]);
    }

    /**
     * Deactivate admin (soft delete)
     */
    public function destroy(Admin $admin): JsonResponse
    {
        $admin->update(['is_active' => false]);

        return response()->json([
            'message' => 'Admin deactivated successfully'
        ]);
    }
}
