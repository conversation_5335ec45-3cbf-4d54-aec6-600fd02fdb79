<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AttendanceController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Attendance index - not implemented yet']);
    }

    public function mark(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Mark attendance - not implemented yet']);
    }

    public function today(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Today attendance - not implemented yet']);
    }

    public function userHistory(Request $request, $user): JsonResponse
    {
        return response()->json(['message' => 'User history - not implemented yet']);
    }

    public function bulkMark(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Bulk mark - not implemented yet']);
    }

    public function update(Request $request, $attendance): JsonResponse
    {
        return response()->json(['message' => 'Update attendance - not implemented yet']);
    }

    public function destroy(Request $request, $attendance): JsonResponse
    {
        return response()->json(['message' => 'Delete attendance - not implemented yet']);
    }

    public function scanQr(Request $request): JsonResponse
    {
        return response()->json(['message' => 'QR scan - not implemented yet']);
    }
}
