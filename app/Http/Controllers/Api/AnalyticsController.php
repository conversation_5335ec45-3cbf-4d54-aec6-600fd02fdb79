<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AnalyticsController extends Controller
{
    public function dashboard(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Dashboard analytics - not implemented yet']);
    }

    public function trends(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Trends analytics - not implemented yet']);
    }

    public function performance(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Performance analytics - not implemented yet']);
    }
}
