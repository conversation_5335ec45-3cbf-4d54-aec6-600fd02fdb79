<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use App\Models\Admin;

class AuthController extends Controller
{
    /**
     * Handle user login and issue API token
     */
    public function login(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        $admin = Admin::where('email', $request->email)->first();

        if (!$admin || !Hash::check($request->password, $admin->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        // Update last login
        $admin->updateLastLogin();

        // Delete existing tokens
        $admin->tokens()->delete();

        // Create new token
        $token = $admin->createToken('api-token')->plainTextToken;

        return response()->json([
            'admin' => $admin,
            'token' => $token,
            'expires_at' => now()->addYear(),
        ]);
    }

    /**
     * Handle user logout and revoke token
     */
    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Refresh the admin's token
     */
    public function refresh(Request $request): JsonResponse
    {
        $admin = $request->user();

        // Delete current token
        $request->user()->currentAccessToken()->delete();

        // Create new token
        $token = $admin->createToken('api-token')->plainTextToken;

        return response()->json([
            'admin' => $admin,
            'token' => $token,
            'expires_at' => now()->addYear(),
        ]);
    }

    /**
     * Get authenticated admin details
     */
    public function user(Request $request): JsonResponse
    {
        return response()->json([
            'admin' => $request->user()->load('roles', 'permissions')
        ]);
    }
}
