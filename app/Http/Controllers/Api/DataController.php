<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DataController extends Controller
{
    public function importUsers(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Import users - not implemented yet']);
    }

    public function importAttendance(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Import attendance - not implemented yet']);
    }

    public function exportUsers(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Export users - not implemented yet']);
    }

    public function exportAttendance(Request $request): JsonResponse
    {
        return response()->json(['message' => 'Export attendance - not implemented yet']);
    }

    public function downloadTemplate(Request $request, $type): JsonResponse
    {
        return response()->json(['message' => 'Download template - not implemented yet']);
    }
}
