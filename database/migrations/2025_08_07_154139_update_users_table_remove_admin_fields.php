<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Disable transactions for this migration
     */
    public $withinTransaction = false;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, migrate existing admin users to the admins table
        DB::statement("
            INSERT INTO admins (name, email, email_verified_at, password, remember_token, is_active, created_at, updated_at)
            SELECT name, email, email_verified_at, password, remember_token, is_active, created_at, updated_at
            FROM users
            WHERE email IS NOT NULL AND password IS NOT NULL
        ");

        // Remove admin users from users table
        DB::statement("DELETE FROM users WHERE email IS NOT NULL AND password IS NOT NULL");

        // Remove admin-specific columns from users table
        DB::statement("ALTER TABLE users DROP COLUMN IF EXISTS email");
        DB::statement("ALTER TABLE users DROP COLUMN IF EXISTS email_verified_at");
        DB::statement("ALTER TABLE users DROP COLUMN IF EXISTS password");
        DB::statement("ALTER TABLE users DROP COLUMN IF EXISTS remember_token");

        // Make phone required for users (attendance users)
        DB::statement("ALTER TABLE users ALTER COLUMN phone SET NOT NULL");
        DB::statement("ALTER TABLE users ALTER COLUMN gender SET NOT NULL");
        DB::statement("ALTER TABLE users ALTER COLUMN birth_date SET NOT NULL");
        DB::statement("ALTER TABLE users ALTER COLUMN college SET NOT NULL");
        DB::statement("ALTER TABLE users ALTER COLUMN academic_year SET NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back admin columns to users table
        DB::statement("ALTER TABLE users ADD COLUMN email VARCHAR(255)");
        DB::statement("ALTER TABLE users ADD COLUMN email_verified_at TIMESTAMP");
        DB::statement("ALTER TABLE users ADD COLUMN password VARCHAR(255)");
        DB::statement("ALTER TABLE users ADD COLUMN remember_token VARCHAR(100)");

        // Make phone nullable again
        DB::statement("ALTER TABLE users ALTER COLUMN phone DROP NOT NULL");
        DB::statement("ALTER TABLE users ALTER COLUMN gender DROP NOT NULL");
        DB::statement("ALTER TABLE users ALTER COLUMN birth_date DROP NOT NULL");
        DB::statement("ALTER TABLE users ALTER COLUMN college DROP NOT NULL");
        DB::statement("ALTER TABLE users ALTER COLUMN academic_year DROP NOT NULL");

        // Migrate admins back to users table
        DB::statement("
            INSERT INTO users (name, email, email_verified_at, password, remember_token, is_active, created_at, updated_at)
            SELECT name, email, email_verified_at, password, remember_token, is_active, created_at, updated_at
            FROM admins
        ");
    }
};
