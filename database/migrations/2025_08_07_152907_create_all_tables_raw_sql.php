<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Disable transactions for this migration
     */
    public $withinTransaction = false;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create users table
        DB::statement('
            CREATE TABLE users (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255),
                email_verified_at TIMESTAMP,
                password VARCHAR(255),
                remember_token VARCHAR(100),
                phone VARCHAR(20),
                gender VARCHAR(10),
                birth_date DATE,
                address TEXT,
                facebook_url VARCHAR(255),
                college VARCHAR(100),
                academic_year SMALLINT,
                department VARCHAR(100),
                first_attendance_date DATE,
                qr_token VARCHAR(100),
                is_active BOOLEAN DEFAULT true,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                deleted_at TIMESTAMP
            )
        ');

        // Create attendances table
        DB::statement('
            CREATE TABLE attendances (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                meeting_date DATE NOT NULL,
                status VARCHAR(20) NOT NULL,
                marked_by VARCHAR(255),
                marked_at TIMESTAMP,
                method VARCHAR(20) DEFAULT \'manual\',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ');

        // Create settings table
        DB::statement('
            CREATE TABLE settings (
                id BIGSERIAL PRIMARY KEY,
                key VARCHAR(100) NOT NULL,
                value JSONB NOT NULL,
                description TEXT,
                category VARCHAR(50) DEFAULT \'general\',
                is_public BOOLEAN DEFAULT false,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ');

        // Create notifications table
        DB::statement('
            CREATE TABLE notifications (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT,
                type VARCHAR(50) NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                data JSONB,
                sent_at TIMESTAMP,
                status VARCHAR(20) DEFAULT \'pending\',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ');

        // Create import_logs table
        DB::statement('
            CREATE TABLE import_logs (
                id BIGSERIAL PRIMARY KEY,
                file_name VARCHAR(255) NOT NULL,
                file_size BIGINT,
                import_type VARCHAR(50) NOT NULL,
                total_records INTEGER DEFAULT 0,
                successful_records INTEGER DEFAULT 0,
                failed_records INTEGER DEFAULT 0,
                errors JSONB,
                imported_by VARCHAR(255),
                status VARCHAR(20) DEFAULT \'processing\',
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ');

        // Create personal_access_tokens table for Sanctum
        DB::statement('
            CREATE TABLE personal_access_tokens (
                id BIGSERIAL PRIMARY KEY,
                tokenable_type VARCHAR(255) NOT NULL,
                tokenable_id BIGINT NOT NULL,
                name TEXT NOT NULL,
                token VARCHAR(64) NOT NULL,
                abilities TEXT,
                last_used_at TIMESTAMP,
                expires_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP TABLE IF EXISTS personal_access_tokens');
        DB::statement('DROP TABLE IF EXISTS import_logs');
        DB::statement('DROP TABLE IF EXISTS notifications');
        DB::statement('DROP TABLE IF EXISTS settings');
        DB::statement('DROP TABLE IF EXISTS attendances');
        DB::statement('DROP TABLE IF EXISTS users');
    }
};
