<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if required columns exist
        if (!Schema::hasColumn('users', 'academic_year')) {
            return; // safety: raw SQL migration may have already created different schema
        }

        // Use raw SQL to create indexes safely
        $indexes = [
            'idx_users_academic_year' => 'CREATE INDEX IF NOT EXISTS idx_users_academic_year ON users (academic_year)',
            'idx_users_is_active' => 'CREATE INDEX IF NOT EXISTS idx_users_is_active ON users (is_active)',
            'idx_users_active_year' => 'CREATE INDEX IF NOT EXISTS idx_users_active_year ON users (is_active, academic_year)',
            'idx_users_phone' => 'CREATE INDEX IF NOT EXISTS idx_users_phone ON users (phone)',
            'idx_users_college' => 'CREATE INDEX IF NOT EXISTS idx_users_college ON users (college)',
            'idx_users_department' => 'CREATE INDEX IF NOT EXISTS idx_users_department ON users (department)',
            'idx_users_year_created' => 'CREATE INDEX IF NOT EXISTS idx_users_year_created ON users (academic_year, created_at)',
            'idx_users_year_name' => 'CREATE INDEX IF NOT EXISTS idx_users_year_name ON users (academic_year, name)',
            'idx_users_active_created' => 'CREATE INDEX IF NOT EXISTS idx_users_active_created ON users (is_active, created_at)',
            'idx_users_active_name' => 'CREATE INDEX IF NOT EXISTS idx_users_active_name ON users (is_active, name)',
            'idx_users_created_at' => 'CREATE INDEX IF NOT EXISTS idx_users_created_at ON users (created_at)',
            'idx_users_first_attendance' => 'CREATE INDEX IF NOT EXISTS idx_users_first_attendance ON users (first_attendance_date)',
            'idx_users_birth_date' => 'CREATE INDEX IF NOT EXISTS idx_users_birth_date ON users (birth_date)',
            'idx_users_active_year_created' => 'CREATE INDEX IF NOT EXISTS idx_users_active_year_created ON users (is_active, academic_year, created_at)',
        ];

        foreach ($indexes as $name => $sql) {
            try {
                DB::statement($sql);
                \Log::info("Successfully created index: {$name}");
            } catch (\Exception $e) {
                \Log::warning("Failed to create index {$name}: " . $e->getMessage());
            }
        }

        // Add full-text search indexes for PostgreSQL
        if (config('database.default') === 'pgsql') {
            $fullTextIndexes = [
                'idx_users_search_name' => "CREATE INDEX IF NOT EXISTS idx_users_search_name ON users USING gin(to_tsvector('arabic', name))",
                'idx_users_search_college' => "CREATE INDEX IF NOT EXISTS idx_users_search_college ON users USING gin(to_tsvector('arabic', college))",
                'idx_users_search_department' => "CREATE INDEX IF NOT EXISTS idx_users_search_department ON users USING gin(to_tsvector('arabic', department))",
            ];

            foreach ($fullTextIndexes as $name => $sql) {
                try {
                    DB::statement($sql);
                    \Log::info("Successfully created full-text index: {$name}");
                } catch (\Exception $e) {
                    \Log::warning("Failed to create full-text index {$name}: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop full-text search indexes
        if (config('database.default') === 'pgsql') {
            DB::statement("DROP INDEX CONCURRENTLY IF EXISTS idx_users_search_name");
            DB::statement("DROP INDEX CONCURRENTLY IF EXISTS idx_users_search_college");
            DB::statement("DROP INDEX CONCURRENTLY IF EXISTS idx_users_search_department");
        }

        Schema::table('users', function (Blueprint $table) {
            // Drop all indexes
            $table->dropIndex('idx_users_academic_year');
            $table->dropIndex('idx_users_is_active');
            $table->dropIndex('idx_users_active_year');
            $table->dropIndex('idx_users_phone');
            $table->dropIndex('idx_users_college');
            $table->dropIndex('idx_users_department');
            $table->dropIndex('idx_users_year_created');
            $table->dropIndex('idx_users_year_name');
            $table->dropIndex('idx_users_active_created');
            $table->dropIndex('idx_users_active_name');
            $table->dropIndex('idx_users_created_at');
            $table->dropIndex('idx_users_first_attendance');
            $table->dropIndex('idx_users_birth_date');
            $table->dropIndex('idx_users_active_year_created');
        });
    }
};

