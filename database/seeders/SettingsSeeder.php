<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $defaultSettings = [
            [
                'key' => 'auto_mark_absent',
                'value' => json_encode(true),
                'description' => 'Automatically mark users as absent if not present by cutoff time',
                'category' => 'attendance',
                'is_public' => false,
            ],
            [
                'key' => 'absent_cutoff_time',
                'value' => json_encode('21:00'),
                'description' => 'Time after which users are automatically marked absent',
                'category' => 'attendance',
                'is_public' => false,
            ],
            [
                'key' => 'birthday_notifications',
                'value' => json_encode(true),
                'description' => 'Enable automatic birthday notifications',
                'category' => 'notifications',
                'is_public' => false,
            ],
            [
                'key' => 'notification_frequency',
                'value' => json_encode('weekly'),
                'description' => 'Frequency of follow-up notifications',
                'category' => 'notifications',
                'is_public' => false,
            ],
            [
                'key' => 'whatsapp_template',
                'value' => json_encode('كل سنة وانت طيب يا {{name}}! 🎉'),
                'description' => 'WhatsApp birthday message template',
                'category' => 'notifications',
                'is_public' => false,
            ],
            [
                'key' => 'meeting_days',
                'value' => json_encode(['friday']),
                'description' => 'Days of the week when meetings occur',
                'category' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'academic_year_start',
                'value' => json_encode('2024-09-01'),
                'description' => 'Start date of the current academic year',
                'category' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'organization_name',
                'value' => json_encode('اجتماع الأنبا أرسانيوس لشباب كليتي علوم وتربية'),
                'description' => 'Full organization name',
                'category' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'contact_phone',
                'value' => json_encode('01234567890'),
                'description' => 'Organization contact phone number',
                'category' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'backup_frequency',
                'value' => json_encode('weekly'),
                'description' => 'Frequency of automatic database backups',
                'category' => 'system',
                'is_public' => false,
            ],
        ];

        foreach ($defaultSettings as $setting) {
            Setting::firstOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('Default settings created successfully!');
    }
}
