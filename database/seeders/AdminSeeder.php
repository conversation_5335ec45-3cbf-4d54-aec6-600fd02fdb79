<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\Admin;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin users
        $admins = [
            [
                'name' => 'مينا صفوت',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'super_admin',
                'is_active' => true,
            ],
            [
                'name' => 'سيلفيا سامح',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'receptionist',
                'is_active' => true,
            ],
            [
                'name' => 'ابانوب نشأت',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'admin',
                'is_active' => true,
            ],
        ];

        foreach ($admins as $adminData) {
            Admin::firstOrCreate(
                ['email' => $adminData['email']],
                $adminData
            );
        }

        $this->command->info('Admin users created successfully!');
        $this->command->info('Default password for all admins: password123');
        $this->command->info('Login emails:');
        $this->command->info('- <EMAIL> (Super Admin)');
        $this->command->info('- <EMAIL> (Receptionist)');
        $this->command->info('- <EMAIL> (Admin)');
    }
}
