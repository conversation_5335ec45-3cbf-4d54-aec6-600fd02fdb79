<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;


class AdminUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin users
        $adminUsers = [
            [
                'name' => 'مينا صفوت',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'is_active' => true,
            ],
            [
                'name' => 'سيلفيا سامح',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'is_active' => true,
            ],
            [
                'name' => 'ابانوب نشأت',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'is_active' => true,
            ],
        ];

        foreach ($adminUsers as $userData) {
            User::firstOrCreate(
                ['email' => $userData['email']],
                $userData
            );
        }

        $this->command->info('Admin users created successfully!');
        $this->command->info('Default password for all users: password123');
        $this->command->info('Login emails:');
        $this->command->info('- <EMAIL>');
        $this->command->info('- <EMAIL>');
        $this->command->info('- <EMAIL>');
    }
}
