<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class UsersFromExcelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting Excel import for Arsanios Attendance System...');

        // Path to your Excel file (place it in storage/app/imports/)
        $excelPath = storage_path('app/imports/arsanios_users.xlsx');

        if (!file_exists($excelPath)) {
            $this->command->error("❌ Excel file not found at: {$excelPath}");
            $this->command->info("📁 Please place your Excel file at: storage/app/imports/arsanios_users.xlsx");
            return;
        }

        try {
            $spreadsheet = IOFactory::load($excelPath);
            $totalImported = 0;
            $errors = [];

            // Process each sheet (Year 1, 2, 3, 4)
            for ($year = 1; $year <= 4; $year++) {
                $this->command->info("📊 Processing Year {$year}...");

                try {
                    $worksheet = $spreadsheet->getSheet($year - 1); // Sheets are 0-indexed
                    $imported = $this->processSheet($worksheet, $year);
                    $totalImported += $imported;
                    $this->command->info("✅ Year {$year}: {$imported} users imported");
                } catch (\Exception $e) {
                    $error = "❌ Error processing Year {$year}: " . $e->getMessage();
                    $errors[] = $error;
                    $this->command->error($error);
                }
            }

            $this->command->info("🎉 Import completed!");
            $this->command->info("📈 Total users imported: {$totalImported}");

            if (!empty($errors)) {
                $this->command->warn("⚠️  Some errors occurred:");
                foreach ($errors as $error) {
                    $this->command->warn($error);
                }
            }

        } catch (\Exception $e) {
            $this->command->error("❌ Failed to read Excel file: " . $e->getMessage());
        }
    }

    private function processSheet($worksheet, $academicYear)
    {
        $highestRow = $worksheet->getHighestRow();
        $imported = 0;
        $errors = [];

        // Skip header row, start from row 2
        for ($row = 2; $row <= $highestRow; $row++) {
            try {
                $data = $this->extractRowData($worksheet, $row, $academicYear);

                // Skip empty rows
                if ($this->isEmptyRow($data)) {
                    continue;
                }

                $validationResult = $this->validateAndSanitizeUserData($data, $row);

                if ($validationResult['valid']) {
                    $this->createUser($validationResult['data']);
                    $imported++;
                } else {
                    $errors = array_merge($errors, $validationResult['errors']);
                    $this->command->warn("⚠️  Row {$row}: " . implode(', ', $validationResult['errors']));
                }
            } catch (\Exception $e) {
                $error = "Row {$row}: Unexpected error - " . $e->getMessage();
                $errors[] = $error;
                $this->command->warn("⚠️  {$error}");
            }
        }

        // Show summary
        if (!empty($errors)) {
            $this->command->warn("📋 Year {$academicYear} Summary: {$imported} imported, " . count($errors) . " errors");
        }

        return $imported;
    }

    private function extractRowData($worksheet, $row, $academicYear)
    {
        // Extract raw data from Excel columns (A=1, B=2, etc.)
        $rawData = [
            'name' => $worksheet->getCell("A{$row}")->getValue(),
            'phone' => $worksheet->getCell("B{$row}")->getValue(),
            'gender' => $worksheet->getCell("C{$row}")->getValue(),
            'birth_date' => $worksheet->getCell("D{$row}")->getValue(),
            'address' => $worksheet->getCell("E{$row}")->getValue(),
            'facebook_url' => $worksheet->getCell("F{$row}")->getValue(),
            'college' => $worksheet->getCell("G{$row}")->getValue(),
            'department' => $worksheet->getCell("H{$row}")->getValue(),
            'notes' => $worksheet->getCell("I{$row}")->getValue(),
            'academic_year' => $academicYear,
        ];

        // Handle Excel date conversion for birth_date
        if (Date::isDateTime($worksheet->getCell("D{$row}"))) {
            try {
                $rawData['birth_date'] = Date::excelToDateTimeObject($rawData['birth_date'])->format('Y-m-d');
            } catch (\Exception $e) {
                $rawData['birth_date'] = null;
            }
        }

        return $rawData;
    }

    private function isEmptyRow($data)
    {
        // Check if all important fields are empty
        $importantFields = ['name', 'phone', 'college'];

        foreach ($importantFields as $field) {
            if (!empty(trim($data[$field] ?? ''))) {
                return false;
            }
        }

        return true;
    }

    private function validateAndSanitizeUserData($data, $rowNumber)
    {
        $errors = [];
        $sanitizedData = [];

        // Sanitize and validate name (required)
        $name = $this->sanitizeText($data['name'] ?? '');
        if (empty($name)) {
            $errors[] = "Name is required";
        } else {
            $sanitizedData['name'] = $name;
        }

        // Sanitize and validate phone (required)
        $rawPhone = $data['phone'] ?? '';
        $phone = $this->sanitizePhone($rawPhone);
        if ($rawPhone !== '' && $phone === '') {
            $errors[] = "Phone contains unsupported characters (raw digits may be non-ASCII). Raw: '{$rawPhone}'";
        } elseif (empty($phone)) {
            $errors[] = "Phone is required";
        } elseif (!$this->isValidPhone($phone)) {
            $errors[] = "Invalid phone format (must be 11 digits starting with 01) [raw: '{$rawPhone}', sanitized: '{$phone}', len: " . strlen($phone) . "]";
        } else {
            $sanitizedData['phone'] = $phone;
        }

        // Sanitize and validate gender (required)
        $gender = $this->sanitizeGender($data['gender'] ?? '');
        if (empty($gender)) {
            $errors[] = "Gender is required";
        } elseif (!in_array($gender, ['male', 'female'])) {
            $errors[] = "Invalid gender (must be 'male' or 'female')";
        } else {
            $sanitizedData['gender'] = $gender;
        }

        // Validate and sanitize birth_date (required)
        $birthDate = $this->sanitizeDate($data['birth_date'] ?? '');
        if (empty($birthDate)) {
            $errors[] = "Birth date is required";
        } else {
            $sanitizedData['birth_date'] = $birthDate;
        }

        // Sanitize and validate college (required)
        $college = $this->sanitizeText($data['college'] ?? '');
        if (empty($college)) {
            $errors[] = "College is required";
        } else {
            $sanitizedData['college'] = $college;
        }

        // Validate academic year (required)
        $academicYear = (int)($data['academic_year'] ?? 0);
        if (!in_array($academicYear, [1, 2, 3, 4])) {
            $errors[] = "Invalid academic year (must be 1, 2, 3, or 4)";
        } else {
            $sanitizedData['academic_year'] = $academicYear;
        }

        // Optional fields with NULL handling
        $sanitizedData['address'] = $this->sanitizeText($data['address'] ?? '') ?: 'المنيا - مصر';
        $sanitizedData['facebook_url'] = $this->sanitizeUrl($data['facebook_url'] ?? '');
        $sanitizedData['department'] = $this->sanitizeText($data['department'] ?? '');
        $sanitizedData['notes'] = $this->sanitizeText($data['notes'] ?? '');

        // System fields
        $sanitizedData['first_attendance_date'] = null; // Always NULL on import
        $sanitizedData['qr_token'] = Str::uuid();
        $sanitizedData['is_active'] = true;

        return [
            'valid' => empty($errors),
            'data' => $sanitizedData,
            'errors' => $errors
        ];
    }

    private function createUser($data)
    {
        // Check if user already exists (by phone number)
        $existingUser = User::where('phone', $data['phone'])->first();

        if ($existingUser) {
            $this->command->warn("⚠️  User with phone {$data['phone']} already exists, skipping...");
            return;
        }

        User::create($data);
    }

    // Data sanitization helper methods
    private function sanitizeText($value)
    {
        if (is_null($value) || $value === '') {
            return null;
        }

        $cleaned = trim($value);
        return $cleaned === '' ? null : $cleaned;
    }

    private function sanitizePhone($phone)
    {
        if ($phone === null) {
            return '';
        }

        // Normalize to string
        $val = (string)$phone;

        // Replace Arabic-Indic digits with Western digits
        $arabicIndic = ['٠','١','٢','٣','٤','٥','٦','٧','٨','٩'];
        $easternArabicIndic = ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'];
        $western = ['0','1','2','3','4','5','6','7','8','9'];
        $val = str_replace($arabicIndic, $western, $val);
        $val = str_replace($easternArabicIndic, $western, $val);

        // Strip zero-width and non-breaking spaces and other invisibles
        $invisibles = ["\u{200B}", "\u{200C}", "\u{200D}", "\u{FEFF}", "\u{00A0}"]; // ZWSP, ZWNJ, ZWJ, BOM, NBSP
        foreach ($invisibles as $ch) { $val = str_replace($ch, '', $val); }

        // Remove all characters except digits
        $digits = preg_replace('/[^0-9]/u', '', $val);

        if ($digits === '') {
            return '';
        }

        // Handle common Egypt country code variants: +20, 0020, 20
        // Remove leading plus if present
        $digits = ltrim($digits, '+');

        // If starts with 0020, drop 0020
        if (str_starts_with($digits, '0020')) {
            $digits = substr($digits, 4);
        }
        // If starts with 20 and length > 11, drop 20
        if (str_starts_with($digits, '20') && strlen($digits) > 11) {
            $digits = substr($digits, 2);
        }

        // If still longer than 11 but starts with 2 (sometimes extra leading 2), trim one 2
        if (strlen($digits) > 11 && str_starts_with($digits, '2')) {
            $digits = substr($digits, 1);
        }

        // If more than 11 digits remain, keep the last 11 (common when country codes included twice)
        if (strlen($digits) > 11) {
            $digits = substr($digits, -11);
        }

        return $digits;
    }

    private function isValidPhone($phone)
    {
        return preg_match('/^01[0-9]{9}$/', $phone);
    }

    private function sanitizeGender($gender)
    {
        if (is_null($gender) || $gender === '') {
            return '';
        }

        $cleaned = strtolower(trim($gender));

        // Handle various gender formats
        $maleVariants = ['male', 'm', 'ذكر', 'ذ'];
        $femaleVariants = ['female', 'f', 'أنثى', 'انثى', 'ا'];

        if (in_array($cleaned, $maleVariants)) {
            return 'male';
        } elseif (in_array($cleaned, $femaleVariants)) {
            return 'female';
        }

        return $cleaned; // Return as-is for validation to catch invalid values
    }

    private function sanitizeDate($date)
    {
        if (is_null($date) || $date === '') {
            return null;
        }

        try {
            // If it's already in Y-m-d format from Excel conversion, validate it
            if (is_string($date) && preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                $dateObj = \DateTime::createFromFormat('Y-m-d', $date);
                if ($dateObj !== false) {
                    // Validate the date
                    if ($dateObj > new \DateTime()) {
                        return null; // Future date
                    }
                    if ($dateObj->format('Y') < 1950) {
                        return null; // Too old
                    }
                    if ($dateObj->format('Y-m-d') === '1900-01-01') {
                        return null; // Excel default
                    }
                    return $date;
                }
            }

            // Handle various date formats
            $timestamp = null;

            // Try different date formats
            $formats = ['Y-m-d', 'd/m/Y', 'm/d/Y', 'd-m-Y', 'Y/m/d'];

            foreach ($formats as $format) {
                $dateObj = \DateTime::createFromFormat($format, trim($date));
                if ($dateObj !== false) {
                    $timestamp = $dateObj->getTimestamp();
                    break;
                }
            }

            // If no format worked, try strtotime
            if ($timestamp === null) {
                $timestamp = strtotime(trim($date));
            }

            if ($timestamp === false) {
                return null;
            }

            $dateObj = new \DateTime();
            $dateObj->setTimestamp($timestamp);

            // Reject future dates
            if ($dateObj > new \DateTime()) {
                return null;
            }

            // Reject dates before 1950 (likely invalid)
            if ($dateObj->format('Y') < 1950) {
                return null;
            }

            // Reject Excel's default date (1900-01-01)
            if ($dateObj->format('Y-m-d') === '1900-01-01') {
                return null;
            }

            return $dateObj->format('Y-m-d');

        } catch (\Exception $e) {
            return null;
        }
    }

    private function sanitizeUrl($url)
    {
        if (is_null($url) || $url === '') {
            return null;
        }

        $cleaned = trim($url);

        if ($cleaned === '') {
            return null;
        }

        // Add protocol if missing
        if (!preg_match('/^https?:\/\//', $cleaned)) {
            $cleaned = 'https://' . $cleaned;
        }

        // Basic URL validation
        if (filter_var($cleaned, FILTER_VALIDATE_URL)) {
            return $cleaned;
        }

        return null;
    }
}
