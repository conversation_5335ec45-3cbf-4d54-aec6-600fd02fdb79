<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $orgName }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            width: 90%;
            text-align: center;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .title {
            font-size: 2rem;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(5px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #fff;
        }
        
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
            margin-top: 5px;
        }
        
        .api-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: right;
        }
        
        .api-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #fff;
        }
        
        .api-endpoint {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            text-align: left;
            direction: ltr;
        }
        
        .contact {
            margin-top: 30px;
            font-size: 1.1rem;
        }
        
        .contact a {
            color: #fff;
            text-decoration: none;
            font-weight: bold;
        }
        
        .contact a:hover {
            text-decoration: underline;
        }
        
        .footer {
            margin-top: 40px;
            opacity: 0.8;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">⛪</div>
        <h1 class="title">{{ $orgName }}</h1>
        <p class="subtitle">نظام إدارة الحضور الإلكتروني</p>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ $stats['admins'] }}</div>
                <div class="stat-label">المديرين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['users'] }}</div>
                <div class="stat-label">الأعضاء</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['attendances_today'] }}</div>
                <div class="stat-label">حضور اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['total_attendances'] }}</div>
                <div class="stat-label">إجمالي الحضور</div>
            </div>
        </div>
        
        <div class="api-section">
            <h3 class="api-title">🔗 API Endpoints</h3>
            <div class="api-endpoint">POST /api/auth/login - Admin Authentication</div>
            <div class="api-endpoint">GET /api/admins - Manage Administrators</div>
            <div class="api-endpoint">GET /api/users - Manage Users</div>
            <div class="api-endpoint">POST /api/attendance/mark - Mark Attendance</div>
            <div class="api-endpoint">GET /api/reports/weekly - Weekly Reports</div>
        </div>
        
        <div class="contact">
            📞 للتواصل: <a href="tel:{{ $contactPhone }}">{{ $contactPhone }}</a>
        </div>
        
        <div class="footer">
            <p>تم تطوير النظام بواسطة فريق التطوير التقني</p>
            <p>جميع الحقوق محفوظة © {{ date('Y') }}</p>
        </div>
    </div>
</body>
</html>
