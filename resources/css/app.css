@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans: 'Cairo', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-cairo: 'Cairo', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.87 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.87 0 0);

    /* Enhanced Modern Design Variables */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

    /* Enhanced Gradients */
    --gradient-primary: linear-gradient(135deg, oklch(0.646 0.222 41.116), oklch(0.6 0.118 184.704));
    --gradient-secondary: linear-gradient(135deg, oklch(0.398 0.07 227.392), oklch(0.828 0.189 84.429));
    --gradient-accent: linear-gradient(135deg, oklch(0.769 0.188 70.08), oklch(0.646 0.222 41.116));
    --gradient-surface: linear-gradient(135deg, oklch(0.985 0 0), oklch(0.97 0 0));

    /* Animation Timing */
    --duration-75: 75ms;
    --duration-100: 100ms;
    --duration-150: 150ms;
    --duration-200: 200ms;
    --duration-300: 300ms;
    --duration-500: 500ms;
    --duration-700: 700ms;
    --duration-1000: 1000ms;

    /* Easing Functions */
    --ease-linear: linear;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.985 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.439 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.985 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background font-cairo text-foreground;
        font-feature-settings:
            'kern' 1,
            'liga' 1,
            'calt' 1;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Enhanced RTL Support and Arabic Styles */
[dir='rtl'] {
    direction: rtl;
    text-align: right;
}

[dir='ltr'] {
    direction: ltr;
    text-align: left;
}

.font-cairo {
    font-family: 'Cairo', sans-serif;
    font-feature-settings:
        'kern' 1,
        'liga' 1,
        'calt' 1;
}

.text-heading {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

.text-body {
    font-family: 'Cairo', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: 0.01em;
}

/* RTL-specific spacing and layout utilities */
.rtl-space-x-reverse > * + * {
    margin-right: var(--tw-space-x-reverse);
    margin-left: calc(var(--tw-space-x-reverse) * -1);
}

[dir='rtl'] .rtl-space-x-reverse {
    --tw-space-x-reverse: 1;
}

.rtl-mr-auto {
    margin-right: auto;
}

[dir='rtl'] .rtl-mr-auto {
    margin-right: 0;
    margin-left: auto;
}

.rtl-ml-auto {
    margin-left: auto;
}

[dir='rtl'] .rtl-ml-auto {
    margin-left: 0;
    margin-right: auto;
}

/* RTL-specific flexbox utilities */
.rtl-flex-row-reverse {
    flex-direction: row;
}

[dir='rtl'] .rtl-flex-row-reverse {
    flex-direction: row-reverse;
}

/* RTL-specific text alignment */
.rtl-text-left {
    text-align: left;
}

[dir='rtl'] .rtl-text-left {
    text-align: right;
}

.rtl-text-right {
    text-align: right;
}

[dir='rtl'] .rtl-text-right {
    text-align: left;
}

/* RTL-specific positioning */
.rtl-left-0 {
    left: 0;
}

[dir='rtl'] .rtl-left-0 {
    left: auto;
    right: 0;
}

.rtl-right-0 {
    right: 0;
}

[dir='rtl'] .rtl-right-0 {
    right: auto;
    left: 0;
}

/* RTL-specific borders */
.rtl-border-l {
    border-left-width: 1px;
}

[dir='rtl'] .rtl-border-l {
    border-left-width: 0;
    border-right-width: 1px;
}

.rtl-border-r {
    border-right-width: 1px;
}

[dir='rtl'] .rtl-border-r {
    border-right-width: 0;
    border-left-width: 1px;
}

/* RTL-specific padding and margin */
.rtl-pl-4 {
    padding-left: 1rem;
}

[dir='rtl'] .rtl-pl-4 {
    padding-left: 0;
    padding-right: 1rem;
}

.rtl-pr-4 {
    padding-right: 1rem;
}

[dir='rtl'] .rtl-pr-4 {
    padding-right: 0;
    padding-left: 1rem;
}

.rtl-ml-2 {
    margin-left: 0.5rem;
}

[dir='rtl'] .rtl-ml-2 {
    margin-left: 0;
    margin-right: 0.5rem;
}

.rtl-mr-2 {
    margin-right: 0.5rem;
}

[dir='rtl'] .rtl-mr-2 {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Animation Classes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

[dir='rtl'] .animate-slide-in-right {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

[dir='rtl'] .animate-slide-in-left {
    animation: slideInRight 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.4s ease-out;
}

.hover-lift {
    transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.hover-scale {
    transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
    transform: scale(1.02);
}

.btn-gradient {
    background: linear-gradient(135deg, theme('colors.blue.500'), theme('colors.purple.500'));
    color: white;
}

.btn-gradient:hover {
    background: linear-gradient(135deg, theme('colors.blue.600'), theme('colors.purple.600'));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.gradient-text {
    background: linear-gradient(135deg, theme('colors.blue.500'), theme('colors.purple.500'));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-transition {
    animation: fadeIn 0.3s ease-out;
}

/* Enhanced Micro-interactions and Animations */
@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes wiggle {
    0%,
    7%,
    14%,
    21%,
    28%,
    35%,
    42%,
    49%,
    56%,
    63%,
    70%,
    77%,
    84%,
    91%,
    98%,
    100% {
        transform: translateX(0);
    }
    3.5%,
    10.5%,
    17.5%,
    24.5%,
    31.5%,
    38.5%,
    45.5%,
    52.5%,
    59.5%,
    66.5%,
    73.5%,
    80.5%,
    87.5%,
    94.5% {
        transform: translateX(-2px);
    }
}

.animate-bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-slide-up {
    animation: slideUp 0.4s ease-out;
}

.animate-slide-down {
    animation: slideDown 0.4s ease-out;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.animate-wiggle {
    animation: wiggle 0.5s ease-in-out;
}

/* Enhanced Form Interactions */
.form-field-enhanced {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-field-enhanced:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.form-field-enhanced input:focus,
.form-field-enhanced textarea:focus,
.form-field-enhanced select:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    border-color: theme('colors.blue.500');
}

/* Enhanced Button Interactions */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all var(--duration-300) var(--ease-in-out);
    background: var(--gradient-primary);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--duration-500) var(--ease-out);
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    filter: brightness(1.05);
}

.btn-enhanced:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

/* Modern Card Styles */
.card-modern {
    background: var(--gradient-surface);
    border-radius: calc(var(--radius) * 1.5);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border);
    transition: all var(--duration-300) var(--ease-in-out);
}

.card-modern:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-modern-glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-xl);
}

/* Modern Input Styles */
.input-modern {
    background: var(--background);
    border: 2px solid var(--border);
    border-radius: var(--radius);
    transition: all var(--duration-200) var(--ease-in-out);
    box-shadow: var(--shadow-xs);
}

.input-modern:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.input-modern:hover {
    border-color: var(--muted-foreground);
    box-shadow: var(--shadow-sm);
}

/* Modern Typography */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%,
    100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.text-shadow-md {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modern Spacing Utilities */
.space-modern > * + * {
    margin-top: var(--spacing-6);
}

.gap-modern {
    gap: var(--spacing-6);
}

/* Modern Border Styles */
.border-modern {
    border: 1px solid var(--border);
    border-radius: var(--radius);
}

.border-gradient {
    border: 2px solid transparent;
    background:
        linear-gradient(var(--background), var(--background)) padding-box,
        var(--gradient-primary) border-box;
    border-radius: var(--radius);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.loading-dots::after {
    content: '';
    animation: pulse 1.5s infinite;
}

/* Success/Error States */
.success-glow {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
    border-color: theme('colors.green.500');
}

.error-glow {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
    border-color: theme('colors.red.500');
}

.error-shake {
    animation: wiggle 0.5s ease-in-out;
}

/* Progress Animations */
.progress-bar {
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

/* RTL-specific progress bar */
.rtl-progress {
    direction: ltr;
}

[dir='rtl'] .rtl-progress {
    direction: rtl;
}

[dir='rtl'] .rtl-progress .progress-bar {
    transform-origin: right;
}

/* RTL-specific form layouts */
.rtl-form-grid {
    display: grid;
    gap: 1.5rem;
}

[dir='rtl'] .rtl-form-grid {
    direction: rtl;
}

.rtl-form-section {
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

[dir='rtl'] .rtl-form-section {
    text-align: right;
}

.rtl-form-section .form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

[dir='rtl'] .rtl-form-section .form-label {
    flex-direction: row-reverse;
    text-align: right;
}

/* RTL-specific animations */
[dir='rtl'] .animate-slide-in-right {
    animation: slideInLeft 0.6s ease-out;
}

[dir='rtl'] .animate-slide-in-left {
    animation: slideInRight 0.6s ease-out;
}

/* Enhanced Responsive Design */

/* Mobile-first base styles */
.responsive-container {
    width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .responsive-container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .responsive-container {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* Touch-friendly interactions */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.touch-friendly-input {
    min-height: 48px;
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem 1rem;
}

@media (max-width: 768px) {
    .touch-friendly-input {
        min-height: 52px;
        font-size: 16px;
        padding: 1rem;
    }
}

/* Mobile-specific form layouts */
@media (max-width: 640px) {
    .mobile-form-stack {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .mobile-form-stack > * {
        width: 100%;
    }

    .mobile-hide {
        display: none;
    }

    .mobile-full-width {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}

/* Tablet-specific layouts */
@media (min-width: 641px) and (max-width: 1024px) {
    .tablet-grid-2 {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .tablet-stack {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
}

/* Desktop-specific layouts */
@media (min-width: 1025px) {
    .desktop-grid-3 {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }

    .desktop-flex-row {
        display: flex;
        flex-direction: row;
        gap: 2rem;
    }
}

/* Mobile-specific animations */
@media (max-width: 768px) {
    .animate-scale-in {
        animation: slideUp 0.4s ease-out;
    }

    .form-field-enhanced:focus-within {
        transform: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .btn-enhanced:hover {
        transform: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* Reduce motion for mobile users */
    .mobile-reduce-motion {
        animation: none !important;
        transition: none !important;
    }

    /* Larger touch targets for mobile */
    .mobile-touch-large {
        min-height: 56px;
        min-width: 56px;
        padding: 1rem;
    }

    /* Mobile-friendly spacing */
    .mobile-spacing {
        padding: 1rem;
        margin: 0.5rem 0;
    }

    /* Mobile typography */
    .mobile-text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem;
    }

    .mobile-text-base {
        font-size: 1rem;
        line-height: 1.5rem;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .high-dpi-crisp {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .landscape-mobile-compact {
        padding: 0.5rem;
        margin: 0.25rem 0;
    }

    .landscape-mobile-header {
        padding: 1rem 0;
    }
}

/* Print styles */
@media print {
    .print-hide {
        display: none !important;
    }

    .print-show {
        display: block !important;
    }

    .print-page-break {
        page-break-before: always;
    }
}
