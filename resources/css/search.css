/* Search Highlighting Styles */
.search-highlight {
    @apply bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100 px-0.5 rounded-sm font-medium;
    animation: highlight-pulse 0.3s ease-in-out;
}

@keyframes highlight-pulse {
    0% {
        background-color: theme('colors.yellow.300');
        transform: scale(1);
    }
    50% {
        background-color: theme('colors.yellow.400');
        transform: scale(1.02);
    }
    100% {
        background-color: theme('colors.yellow.200');
        transform: scale(1);
    }
}

/* Dark mode highlight */
.dark .search-highlight {
    @apply bg-yellow-800 text-yellow-100;
}

.dark .search-highlight:hover {
    @apply bg-yellow-700;
}

/* Field-specific highlighting */
.search-highlight-name {
    @apply bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100 px-1 rounded font-bold;
}

.search-highlight-phone {
    @apply bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-100 px-1 rounded font-medium;
}

.search-highlight-college {
    @apply bg-purple-100 dark:bg-purple-900 text-purple-900 dark:text-purple-100 px-1 rounded;
}

.search-highlight-department {
    @apply bg-orange-100 dark:bg-orange-900 text-orange-900 dark:text-orange-100 px-1 rounded;
}

/* Search input enhancements */
.search-input-enhanced {
    transition: all 0.2s ease-in-out;
}

.search-input-enhanced:focus {
    box-shadow: 0 0 0 3px theme('colors.blue.100');
    border-color: theme('colors.blue.500');
}

.search-input-enhanced.searching {
    border-color: theme('colors.blue.500');
    background-image: linear-gradient(45deg, transparent 25%, rgba(59, 130, 246, 0.1) 25%, rgba(59, 130, 246, 0.1) 50%, transparent 50%, transparent 75%, rgba(59, 130, 246, 0.1) 75%);
    background-size: 20px 20px;
    animation: search-loading 1s linear infinite;
}

@keyframes search-loading {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 20px;
    }
}

/* Search suggestions dropdown */
.search-suggestions {
    @apply absolute top-full left-0 right-0 z-50 mt-1 max-h-64 overflow-y-auto rounded-lg border border-input bg-background shadow-lg;
    animation: search-suggestions-appear 0.2s ease-out;
}

@keyframes search-suggestions-appear {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.search-suggestion-item {
    @apply w-full px-3 py-2 text-sm text-left hover:bg-muted rounded-md transition-colors cursor-pointer;
}

.search-suggestion-item:hover {
    @apply bg-muted;
}

.search-suggestion-item.focused {
    @apply bg-muted;
}

.search-suggestion-item.recent {
    @apply text-muted-foreground;
}

/* Search performance indicators */
.search-mode-client {
    @apply text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900;
}

.search-mode-server {
    @apply text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900;
}

.search-mode-idle {
    @apply text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-800;
}

/* Search stats */
.search-stats {
    @apply flex items-center gap-2 text-xs text-muted-foreground;
}

.search-stats-item {
    @apply flex items-center gap-1;
}

/* Search result highlighting animations */
.search-result-item {
    transition: all 0.2s ease-in-out;
}

.search-result-item:hover {
    @apply bg-muted/50;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-result-item.highlighted {
    @apply bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800;
    animation: search-result-highlight 0.5s ease-in-out;
}

@keyframes search-result-highlight {
    0% {
        background-color: theme('colors.blue.100');
        transform: scale(1);
    }
    50% {
        background-color: theme('colors.blue.200');
        transform: scale(1.01);
    }
    100% {
        background-color: theme('colors.blue.50');
        transform: scale(1);
    }
}

/* Search loading states */
.search-loading-skeleton {
    @apply animate-pulse bg-muted rounded;
}

.search-loading-dots {
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

.search-loading-dots::after {
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: currentColor;
    animation: search-loading-dots 1.4s infinite ease-in-out both;
}

.search-loading-dots::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: currentColor;
    animation: search-loading-dots 1.4s infinite ease-in-out both;
    animation-delay: -0.16s;
    margin-right: 2px;
}

@keyframes search-loading-dots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Search performance monitor */
.search-performance-monitor {
    @apply fixed bottom-4 right-4 z-50 w-80 shadow-lg border-2 rounded-lg bg-background;
    animation: search-monitor-slide-in 0.3s ease-out;
}

@keyframes search-monitor-slide-in {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.search-performance-monitor.client-mode {
    @apply border-green-500;
}

.search-performance-monitor.server-mode {
    @apply border-blue-500;
}

.search-performance-monitor.idle-mode {
    @apply border-gray-300;
}

/* Search accessibility improvements */
.search-input[aria-expanded="true"] {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.search-suggestions[role="listbox"] {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.search-suggestion-item[role="option"][aria-selected="true"] {
    @apply bg-primary text-primary-foreground;
}

/* RTL support for search */
[dir="rtl"] .search-suggestions {
    @apply text-right;
}

[dir="rtl"] .search-suggestion-item {
    @apply text-right;
}

[dir="rtl"] .search-stats {
    @apply flex-row-reverse;
}

/* Responsive search styles */
@media (max-width: 640px) {
    .search-performance-monitor {
        @apply w-72 bottom-2 right-2;
    }
    
    .search-suggestions {
        @apply max-h-48;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .search-highlight {
        @apply bg-yellow-400 text-black border border-black;
    }
    
    .search-suggestion-item:hover,
    .search-suggestion-item.focused {
        @apply bg-black text-white;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .search-highlight,
    .search-result-item,
    .search-suggestions,
    .search-performance-monitor {
        animation: none;
        transition: none;
    }
    
    .search-input-enhanced.searching {
        animation: none;
        background-image: none;
    }
}
