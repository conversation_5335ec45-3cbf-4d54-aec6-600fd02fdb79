/**
 * Search Functionality Tests
 * 
 * This file contains tests for the enhanced search functionality
 * including fuzzy search, highlighting, and performance monitoring.
 */

import { searchService, SearchService } from '@/lib/search';
import { User } from '@/stores/app-store';

// Mock data for testing
const mockUsers: User[] = [
    {
        id: '1',
        name: 'أحمد محمد علي',
        phone: '01234567890',
        college: 'كلية الهندسة',
        department: 'هندسة الحاسوب',
        gender: 'male',
        year: 1,
        birth_date: '2000-01-01',
        address: 'القاهرة',
        facebook_url: '',
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
    },
    {
        id: '2',
        name: 'فاطمة أحمد حسن',
        phone: '01987654321',
        college: 'كلية الطب',
        department: 'الطب العام',
        gender: 'female',
        year: 2,
        birth_date: '1999-05-15',
        address: 'الإسكندرية',
        facebook_url: '',
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
    },
    {
        id: '3',
        name: 'محم<PERSON> عبد الرحمن',
        phone: '01555666777',
        college: 'كلية الهندسة',
        department: 'هندسة الكهرباء',
        gender: 'male',
        year: 3,
        birth_date: '1998-12-10',
        address: 'الجيزة',
        facebook_url: '',
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
    }
];

describe('Search Service Tests', () => {
    let service: SearchService;

    beforeEach(() => {
        service = new SearchService();
        service.initializeUsers(mockUsers);
    });

    test('should initialize users correctly', () => {
        expect(service).toBeDefined();
        // Test that the service can be initialized without errors
    });

    test('should perform basic search', () => {
        const results = service.searchUsers('أحمد');
        expect(results.length).toBeGreaterThan(0);
        expect(results[0].item.name).toContain('أحمد');
    });

    test('should perform fuzzy search', () => {
        // Test fuzzy matching with slight misspelling
        const results = service.searchUsers('احمد'); // Missing diacritics
        expect(results.length).toBeGreaterThan(0);
    });

    test('should search across multiple fields', () => {
        // Search by college
        const collegeResults = service.searchUsers('هندسة');
        expect(collegeResults.length).toBe(2); // Two engineering students

        // Search by phone
        const phoneResults = service.searchUsers('01234');
        expect(phoneResults.length).toBe(1);
        expect(phoneResults[0].item.phone).toContain('01234');
    });

    test('should apply filters correctly', () => {
        const results = service.searchUsers('محمد', { gender: 'male' });
        expect(results.length).toBeGreaterThan(0);
        results.forEach(result => {
            expect(result.item.gender).toBe('male');
        });
    });

    test('should return highlighted results', () => {
        const results = service.searchUsers('أحمد');
        expect(results.length).toBeGreaterThan(0);
        expect(results[0].highlighted).toBeDefined();
    });

    test('should handle empty search', () => {
        const results = service.searchUsers('');
        expect(results.length).toBe(mockUsers.length);
    });

    test('should handle no results', () => {
        const results = service.searchUsers('xyz123nonexistent');
        expect(results.length).toBe(0);
    });

    test('should sort results by relevance', () => {
        const results = service.searchUsers('محمد');
        expect(results.length).toBeGreaterThan(1);
        
        // Results should be sorted by relevance (lower score is better)
        for (let i = 1; i < results.length; i++) {
            expect(results[i].relevanceScore || 1).toBeGreaterThanOrEqual(results[i-1].relevanceScore || 1);
        }
    });
});

describe('Search Highlighting Tests', () => {
    test('should highlight search matches', () => {
        const text = 'أحمد محمد علي';
        const indices: [number, number][] = [[0, 3]]; // Highlight 'أحمد'
        
        // Mock the highlightMatches function
        const highlightMatches = (text: string, indices: [number, number][]) => {
            if (!indices.length) return text;
            
            let result = '';
            let lastIndex = 0;
            
            indices.forEach(([start, end]) => {
                result += text.slice(lastIndex, start);
                result += `<mark class="search-highlight">${text.slice(start, end + 1)}</mark>`;
                lastIndex = end + 1;
            });
            
            result += text.slice(lastIndex);
            return result;
        };

        const highlighted = highlightMatches(text, indices);
        expect(highlighted).toContain('<mark class="search-highlight">أحمد</mark>');
    });
});

describe('Search Performance Tests', () => {
    test('should complete search within reasonable time', () => {
        const service = new SearchService();
        service.initializeUsers(mockUsers);

        const startTime = performance.now();
        service.searchUsers('محمد');
        const endTime = performance.now();

        const searchTime = endTime - startTime;
        expect(searchTime).toBeLessThan(100); // Should complete within 100ms
    });

    test('should handle large datasets efficiently', () => {
        // Create a larger dataset
        const largeDataset: User[] = [];
        for (let i = 0; i < 1000; i++) {
            largeDataset.push({
                ...mockUsers[0],
                id: `user_${i}`,
                name: `User ${i}`,
                phone: `0123456${i.toString().padStart(4, '0')}`
            });
        }

        const service = new SearchService();
        service.initializeUsers(largeDataset);

        const startTime = performance.now();
        const results = service.searchUsers('User');
        const endTime = performance.now();

        const searchTime = endTime - startTime;
        expect(searchTime).toBeLessThan(500); // Should complete within 500ms
        expect(results.length).toBeGreaterThan(0);
    });
});

describe('Search Suggestions Tests', () => {
    test('should generate search suggestions', () => {
        const service = new SearchService();
        service.initializeUsers(mockUsers);

        const suggestions = service.getUserSuggestions('أح', 5);
        expect(suggestions.length).toBeGreaterThan(0);
        expect(suggestions[0]).toContain('أح');
    });

    test('should limit suggestions count', () => {
        const service = new SearchService();
        service.initializeUsers(mockUsers);

        const suggestions = service.getUserSuggestions('م', 2);
        expect(suggestions.length).toBeLessThanOrEqual(2);
    });
});

// Integration test for the complete search flow
describe('Search Integration Tests', () => {
    test('should perform complete search workflow', () => {
        // Initialize service
        const service = new SearchService();
        service.initializeUsers(mockUsers);

        // Perform search
        const searchQuery = 'أحمد';
        const results = service.searchUsers(searchQuery);

        // Validate results
        expect(results.length).toBeGreaterThan(0);
        expect(results[0].item).toBeDefined();
        expect(results[0].score).toBeDefined();
        expect(results[0].matches).toBeDefined();
        expect(results[0].highlighted).toBeDefined();
        expect(results[0].relevanceScore).toBeDefined();

        // Check highlighting
        const highlighted = results[0].highlighted;
        expect(highlighted).toBeDefined();

        // Get suggestions
        const suggestions = service.getUserSuggestions(searchQuery.substring(0, 2), 5);
        expect(suggestions).toBeDefined();
    });
});

// Mock console methods for testing
const originalConsole = console;
beforeAll(() => {
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
});

afterAll(() => {
    console.log = originalConsole.log;
    console.warn = originalConsole.warn;
    console.error = originalConsole.error;
});

export { mockUsers };
