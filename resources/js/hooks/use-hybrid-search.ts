import { useState, useEffect, useCallback, useMemo } from 'react';
import { searchService, SearchResult } from '@/lib/search';
import { User } from '@/stores/app-store';
import { useDebounce } from './use-debounce';

interface UseHybridSearchOptions {
    serverSearchFn: (query: string) => void;
    debounceMs?: number;
    clientSearchThreshold?: number; // Max items for client-side search
    minSearchLength?: number;
    enableClientSearch?: boolean;
}

interface UseHybridSearchReturn {
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    isSearching: boolean;
    searchResults: SearchResult<User>[];
    clearSearch: () => void;
    searchMode: 'client' | 'server' | 'idle';
    totalResults: number;
    hasMore: boolean;
}

/**
 * Hybrid search hook that uses client-side fuzzy search for small datasets
 * and falls back to server search for larger datasets
 */
export function useHybridSearch(
    users: User[],
    options: UseHybridSearchOptions
): UseHybridSearchReturn {
    const {
        serverSearchFn,
        debounceMs = 300,
        clientSearchThreshold = 500,
        minSearchLength = 1,
        enableClientSearch = true,
    } = options;

    const [searchQuery, setSearchQuery] = useState('');
    const [isSearching, setIsSearching] = useState(false);
    const [searchResults, setSearchResults] = useState<SearchResult<User>[]>([]);
    const [searchMode, setSearchMode] = useState<'client' | 'server' | 'idle'>('idle');

    const debouncedQuery = useDebounce(searchQuery, debounceMs);

    // Initialize search service when users change
    useEffect(() => {
        if (users.length > 0 && enableClientSearch) {
            searchService.initializeUsers(users);
        }
    }, [users, enableClientSearch]);

    // Determine search mode based on data size and query
    const shouldUseClientSearch = useMemo(() => {
        return (
            enableClientSearch &&
            users.length > 0 &&
            users.length <= clientSearchThreshold &&
            debouncedQuery.length >= minSearchLength
        );
    }, [enableClientSearch, users.length, clientSearchThreshold, debouncedQuery, minSearchLength]);

    // Perform search
    useEffect(() => {
        if (!debouncedQuery || debouncedQuery.length < minSearchLength) {
            setSearchResults([]);
            setSearchMode('idle');
            setIsSearching(false);
            return;
        }

        setIsSearching(true);

        if (shouldUseClientSearch) {
            // Client-side fuzzy search
            setSearchMode('client');
            
            try {
                const results = searchService.searchUsers(debouncedQuery);
                setSearchResults(results);
                setIsSearching(false);
            } catch (error) {
                console.warn('Client search failed, falling back to server:', error);
                setSearchMode('server');
                serverSearchFn(debouncedQuery);
            }
        } else {
            // Server-side search
            setSearchMode('server');
            serverSearchFn(debouncedQuery);
        }
    }, [debouncedQuery, shouldUseClientSearch, serverSearchFn, minSearchLength]);

    // Update search results from server (called externally)
    const updateServerResults = useCallback((results: User[], total?: number) => {
        const searchResultsWithScore = results.map(user => ({
            item: user,
            score: 0,
            matches: [],
            highlighted: user,
        }));
        
        setSearchResults(searchResultsWithScore);
        setIsSearching(false);
    }, []);

    // Clear search
    const clearSearch = useCallback(() => {
        setSearchQuery('');
        setSearchResults([]);
        setSearchMode('idle');
        setIsSearching(false);
    }, []);

    // Calculate metrics
    const totalResults = useMemo(() => {
        if (searchMode === 'client') {
            return searchResults.length;
        }
        // For server search, this should be provided externally
        return searchResults.length;
    }, [searchMode, searchResults.length]);

    const hasMore = useMemo(() => {
        if (searchMode === 'client') {
            return false; // Client search returns all results
        }
        // For server search, this should be determined externally
        return false;
    }, [searchMode]);

    return {
        searchQuery,
        setSearchQuery,
        isSearching,
        searchResults,
        clearSearch,
        searchMode,
        totalResults,
        hasMore,
        // Expose update function for server results
        updateServerResults,
    } as UseHybridSearchReturn & { updateServerResults: typeof updateServerResults };
}

/**
 * Hook for getting search suggestions from client-side data
 */
export function useSearchSuggestions(
    users: User[],
    query: string,
    maxSuggestions: number = 5
): Array<{ text: string; type: 'name' | 'college' | 'department'; count: number }> {
    return useMemo(() => {
        if (!query || query.length < 2 || !users.length) {
            return [];
        }

        const suggestions = new Map<string, { type: 'name' | 'college' | 'department'; count: number }>();
        const queryLower = query.toLowerCase();

        users.forEach(user => {
            // Name suggestions
            const nameParts = user.name.toLowerCase().split(' ');
            nameParts.forEach(part => {
                if (part.includes(queryLower) && part.length > 2) {
                    const existing = suggestions.get(part);
                    suggestions.set(part, {
                        type: 'name',
                        count: (existing?.count || 0) + 1
                    });
                }
            });

            // College suggestions
            if (user.college && user.college.toLowerCase().includes(queryLower)) {
                const existing = suggestions.get(user.college);
                suggestions.set(user.college, {
                    type: 'college',
                    count: (existing?.count || 0) + 1
                });
            }

            // Department suggestions
            if (user.department && user.department.toLowerCase().includes(queryLower)) {
                const existing = suggestions.get(user.department);
                suggestions.set(user.department, {
                    type: 'department',
                    count: (existing?.count || 0) + 1
                });
            }
        });

        return Array.from(suggestions.entries())
            .map(([text, { type, count }]) => ({ text, type, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, maxSuggestions);
    }, [users, query, maxSuggestions]);
}

/**
 * Hook for search analytics and performance monitoring
 */
export function useSearchAnalytics() {
    const [searchMetrics, setSearchMetrics] = useState({
        totalSearches: 0,
        clientSearches: 0,
        serverSearches: 0,
        averageSearchTime: 0,
        popularQueries: [] as Array<{ query: string; count: number }>,
    });

    const recordSearch = useCallback((
        query: string,
        mode: 'client' | 'server',
        duration: number,
        resultCount: number
    ) => {
        setSearchMetrics(prev => ({
            ...prev,
            totalSearches: prev.totalSearches + 1,
            clientSearches: mode === 'client' ? prev.clientSearches + 1 : prev.clientSearches,
            serverSearches: mode === 'server' ? prev.serverSearches + 1 : prev.serverSearches,
            averageSearchTime: (prev.averageSearchTime * prev.totalSearches + duration) / (prev.totalSearches + 1),
        }));

        // Track popular queries (simplified)
        if (query.length >= 2) {
            setSearchMetrics(prev => {
                const existing = prev.popularQueries.find(q => q.query === query);
                if (existing) {
                    existing.count++;
                } else {
                    prev.popularQueries.push({ query, count: 1 });
                }
                return {
                    ...prev,
                    popularQueries: prev.popularQueries
                        .sort((a, b) => b.count - a.count)
                        .slice(0, 10)
                };
            });
        }
    }, []);

    return {
        searchMetrics,
        recordSearch,
    };
}
