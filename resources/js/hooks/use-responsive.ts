import { useState, useEffect } from 'react';

interface BreakpointConfig {
    sm: number;
    md: number;
    lg: number;
    xl: number;
    '2xl': number;
}

const defaultBreakpoints: BreakpointConfig = {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536,
};

type Breakpoint = keyof BreakpointConfig;

interface UseResponsiveReturn {
    width: number;
    height: number;
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    isLargeDesktop: boolean;
    currentBreakpoint: Breakpoint;
    isBreakpoint: (breakpoint: Breakpoint) => boolean;
    isAboveBreakpoint: (breakpoint: Breakpoint) => boolean;
    isBelowBreakpoint: (breakpoint: Breakpoint) => boolean;
    orientation: 'portrait' | 'landscape';
    isTouchDevice: boolean;
}

export function useResponsive(breakpoints: BreakpointConfig = defaultBreakpoints): UseResponsiveReturn {
    const [dimensions, setDimensions] = useState({
        width: typeof window !== 'undefined' ? window.innerWidth : 0,
        height: typeof window !== 'undefined' ? window.innerHeight : 0,
    });

    const [isTouchDevice, setIsTouchDevice] = useState(false);

    useEffect(() => {
        // Detect touch device
        const checkTouchDevice = () => {
            setIsTouchDevice(
                'ontouchstart' in window ||
                navigator.maxTouchPoints > 0 ||
                // @ts-ignore
                navigator.msMaxTouchPoints > 0
            );
        };

        checkTouchDevice();

        const handleResize = () => {
            setDimensions({
                width: window.innerWidth,
                height: window.innerHeight,
            });
        };

        window.addEventListener('resize', handleResize);
        window.addEventListener('orientationchange', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
            window.removeEventListener('orientationchange', handleResize);
        };
    }, []);

    const { width, height } = dimensions;

    // Determine current breakpoint
    const getCurrentBreakpoint = (): Breakpoint => {
        if (width >= breakpoints['2xl']) return '2xl';
        if (width >= breakpoints.xl) return 'xl';
        if (width >= breakpoints.lg) return 'lg';
        if (width >= breakpoints.md) return 'md';
        if (width >= breakpoints.sm) return 'sm';
        return 'sm';
    };

    const currentBreakpoint = getCurrentBreakpoint();

    // Device type helpers
    const isMobile = width < breakpoints.md;
    const isTablet = width >= breakpoints.md && width < breakpoints.lg;
    const isDesktop = width >= breakpoints.lg && width < breakpoints.xl;
    const isLargeDesktop = width >= breakpoints.xl;

    // Orientation
    const orientation: 'portrait' | 'landscape' = height > width ? 'portrait' : 'landscape';

    // Breakpoint utilities
    const isBreakpoint = (breakpoint: Breakpoint): boolean => {
        return currentBreakpoint === breakpoint;
    };

    const isAboveBreakpoint = (breakpoint: Breakpoint): boolean => {
        return width >= breakpoints[breakpoint];
    };

    const isBelowBreakpoint = (breakpoint: Breakpoint): boolean => {
        return width < breakpoints[breakpoint];
    };

    return {
        width,
        height,
        isMobile,
        isTablet,
        isDesktop,
        isLargeDesktop,
        currentBreakpoint,
        isBreakpoint,
        isAboveBreakpoint,
        isBelowBreakpoint,
        orientation,
        isTouchDevice,
    };
}

// Hook for media queries
export function useMediaQuery(query: string): boolean {
    const [matches, setMatches] = useState(false);

    useEffect(() => {
        if (typeof window === 'undefined') return;

        const mediaQuery = window.matchMedia(query);
        setMatches(mediaQuery.matches);

        const handler = (event: MediaQueryListEvent) => {
            setMatches(event.matches);
        };

        mediaQuery.addEventListener('change', handler);
        return () => mediaQuery.removeEventListener('change', handler);
    }, [query]);

    return matches;
}

// Hook for container queries (when supported)
export function useContainerQuery(containerRef: React.RefObject<HTMLElement>, query: string): boolean {
    const [matches, setMatches] = useState(false);

    useEffect(() => {
        if (!containerRef.current) return;

        const observer = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const { width, height } = entry.contentRect;
                
                // Simple container query parsing (extend as needed)
                if (query.includes('min-width')) {
                    const minWidth = parseInt(query.match(/min-width:\s*(\d+)px/)?.[1] || '0');
                    setMatches(width >= minWidth);
                } else if (query.includes('max-width')) {
                    const maxWidth = parseInt(query.match(/max-width:\s*(\d+)px/)?.[1] || '0');
                    setMatches(width <= maxWidth);
                }
            }
        });

        observer.observe(containerRef.current);
        return () => observer.disconnect();
    }, [containerRef, query]);

    return matches;
}

// Responsive value hook
export function useResponsiveValue<T>(values: {
    sm?: T;
    md?: T;
    lg?: T;
    xl?: T;
    '2xl'?: T;
    default: T;
}): T {
    const { currentBreakpoint } = useResponsive();

    // Return the value for the current breakpoint or the closest smaller one
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm'];
    const currentIndex = breakpointOrder.indexOf(currentBreakpoint);

    for (let i = currentIndex; i < breakpointOrder.length; i++) {
        const breakpoint = breakpointOrder[i];
        if (values[breakpoint] !== undefined) {
            return values[breakpoint]!;
        }
    }

    return values.default;
}
