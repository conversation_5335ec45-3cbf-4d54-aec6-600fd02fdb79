import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { debounce } from 'lodash-es';

interface SearchCache {
    [key: string]: {
        results: any[];
        timestamp: number;
        totalCount: number;
    };
}

interface UseEnhancedSearchOptions {
    onSearch: (query: string) => void;
    debounceMs?: number;
    cacheTimeout?: number;
    minSearchLength?: number;
    enableCache?: boolean;
}

interface UseEnhancedSearchReturn {
    searchQuery: string;
    isSearching: boolean;
    searchResults: any[];
    searchCache: SearchCache;
    setSearchQuery: (query: string) => void;
    clearSearch: () => void;
    clearCache: () => void;
    getCachedResult: (query: string) => any | null;
    setCachedResult: (query: string, results: any[], totalCount: number) => void;
}

export function useEnhancedSearch({
    onSearch,
    debounceMs = 500,
    cacheTimeout = 5 * 60 * 1000, // 5 minutes
    minSearchLength = 1,
    enableCache = true,
}: UseEnhancedSearchOptions): UseEnhancedSearchReturn {
    const [searchQuery, setSearchQueryState] = useState('');
    const [isSearching, setIsSearching] = useState(false);
    const [searchResults, setSearchResults] = useState<any[]>([]);
    const [searchCache, setSearchCache] = useState<SearchCache>({});
    
    const searchTimeoutRef = useRef<NodeJS.Timeout>();
    const lastSearchRef = useRef<string>('');

    // Memoized debounced search function
    const debouncedSearch = useMemo(
        () => debounce((query: string) => {
            if (query.length >= minSearchLength) {
                // Check cache first
                const cachedResult = getCachedResult(query);
                if (cachedResult && enableCache) {
                    setSearchResults(cachedResult.results);
                    setIsSearching(false);
                    return;
                }
                
                // Perform actual search
                setIsSearching(true);
                lastSearchRef.current = query;
                onSearch(query);
            } else if (query.length === 0) {
                // Clear search
                setSearchResults([]);
                setIsSearching(false);
                onSearch('');
            }
        }, debounceMs),
        [onSearch, debounceMs, minSearchLength, enableCache]
    );

    // Get cached result
    const getCachedResult = useCallback((query: string) => {
        if (!enableCache || !query) return null;
        
        const cached = searchCache[query.toLowerCase()];
        if (!cached) return null;
        
        // Check if cache is still valid
        const isExpired = Date.now() - cached.timestamp > cacheTimeout;
        if (isExpired) {
            // Remove expired cache entry
            setSearchCache(prev => {
                const newCache = { ...prev };
                delete newCache[query.toLowerCase()];
                return newCache;
            });
            return null;
        }
        
        return cached;
    }, [searchCache, cacheTimeout, enableCache]);

    // Set cached result
    const setCachedResult = useCallback((query: string, results: any[], totalCount: number) => {
        if (!enableCache || !query) return;
        
        setSearchCache(prev => ({
            ...prev,
            [query.toLowerCase()]: {
                results,
                totalCount,
                timestamp: Date.now(),
            }
        }));
    }, [enableCache]);

    // Set search query with immediate visual feedback
    const setSearchQuery = useCallback((query: string) => {
        setSearchQueryState(query);
        
        // Clear previous timeout
        if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
        }
        
        // Show searching state immediately for non-empty queries
        if (query.length >= minSearchLength) {
            // Check if we have cached results for instant display
            const cachedResult = getCachedResult(query);
            if (cachedResult && enableCache) {
                setSearchResults(cachedResult.results);
                setIsSearching(false);
            } else {
                setIsSearching(true);
            }
        } else if (query.length === 0) {
            setIsSearching(false);
            setSearchResults([]);
        }
        
        // Trigger debounced search
        debouncedSearch(query);
    }, [debouncedSearch, minSearchLength, getCachedResult, enableCache]);

    // Clear search
    const clearSearch = useCallback(() => {
        setSearchQueryState('');
        setSearchResults([]);
        setIsSearching(false);
        debouncedSearch.cancel();
        onSearch('');
    }, [debouncedSearch, onSearch]);

    // Clear cache
    const clearCache = useCallback(() => {
        setSearchCache({});
    }, []);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            debouncedSearch.cancel();
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }
        };
    }, [debouncedSearch]);

    // Auto-cleanup expired cache entries
    useEffect(() => {
        const cleanupInterval = setInterval(() => {
            const now = Date.now();
            setSearchCache(prev => {
                const newCache = { ...prev };
                let hasChanges = false;
                
                Object.keys(newCache).forEach(key => {
                    if (now - newCache[key].timestamp > cacheTimeout) {
                        delete newCache[key];
                        hasChanges = true;
                    }
                });
                
                return hasChanges ? newCache : prev;
            });
        }, cacheTimeout);

        return () => clearInterval(cleanupInterval);
    }, [cacheTimeout]);

    return {
        searchQuery,
        isSearching,
        searchResults,
        searchCache,
        setSearchQuery,
        clearSearch,
        clearCache,
        getCachedResult,
        setCachedResult,
    };
}
