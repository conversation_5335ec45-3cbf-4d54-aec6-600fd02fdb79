import { apiService, userQueryKeys, type GetUsersParams } from '@/services/api';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';

export interface UseUsersQueryOptions {
    year?: number;
    search?: string;
    page?: number;
    pageSize?: number;
    sortBy?: 'name' | 'academic_year' | 'created_at' | 'birth_date' | 'first_attendance_date';
    sortDir?: 'asc' | 'desc';
    department?: string;
    college?: string;
    gender?: 'male' | 'female';
    hasAttendance?: boolean;
    isActive?: boolean;
    enabled?: boolean;
    enableProgressiveLoading?: boolean;
    enableVirtualScrolling?: boolean;
}

export function useUsersQuery(options: UseUsersQueryOptions = {}) {
    const {
        year = 1,
        search = '',
        page = 1,
        pageSize = 50,
        sortBy = 'name',
        sortDir = 'asc',
        department,
        college,
        gender,
        hasAttendance,
        isActive = true,
        enabled = true,
    } = options;

    // Build API parameters
    const params: GetUsersParams = useMemo(() => {
        const apiParams: GetUsersParams = {
            year,
            page,
            per_page: Math.min(pageSize, 100), // Respect backend limit
            sort_by: sortBy,
            sort_dir: sortDir,
            is_active: isActive,
        };

        // Only add search if it's not empty
        if (search.trim()) {
            apiParams.search = search.trim();
        }

        // Add filter parameters if provided
        if (department) {
            apiParams.department = department;
        }
        if (college) {
            apiParams.college = college;
        }
        if (gender) {
            apiParams.gender = gender;
        }
        if (hasAttendance !== undefined) {
            apiParams.has_attendance = hasAttendance;
        }

        return apiParams;
    }, [year, search, page, pageSize, sortBy, sortDir, department, college, gender, hasAttendance, isActive]);

    // Use TanStack Query
    const query = useQuery({
        queryKey: userQueryKeys.list(params),
        queryFn: () => apiService.getUsersForUI(params),
        enabled,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: (failureCount, error: any) => {
            // Don't retry on validation errors (422) or auth errors (401)
            if (error?.message?.includes('422') || error?.message?.includes('401')) {
                return false;
            }
            return failureCount < 2;
        },
    });

    const queryClient = useQueryClient();

    // Prefetch next page for better UX
    const prefetchNextPage = () => {
        if (query.data?.pagination.current_page < query.data?.pagination.last_page) {
            const nextPageParams = { ...params, page: page + 1 };
            queryClient.prefetchQuery({
                queryKey: userQueryKeys.list(nextPageParams),
                queryFn: () => apiService.getUsersForUI(nextPageParams),
                staleTime: 5 * 60 * 1000,
            });
        }
    };

    // Invalidate and refetch
    const refetch = () => {
        return queryClient.invalidateQueries({
            queryKey: userQueryKeys.lists(),
        });
    };

    // Clear all user cache
    const clearCache = () => {
        queryClient.removeQueries({
            queryKey: userQueryKeys.all,
        });
    };

    return {
        // Data
        data: query.data?.data || [],
        pagination: query.data?.pagination,
        summary: query.data?.summary,
        filterOptions: query.data?.filter_options,
        queryInfo: query.data?.query_info,

        // States
        isLoading: query.isLoading,
        isFetching: query.isFetching,
        isError: query.isError,
        error: query.error,
        isSuccess: query.isSuccess,

        // Actions
        refetch: query.refetch,
        prefetchNextPage,
        invalidate: refetch,
        clearCache,

        // Query info
        queryKey: userQueryKeys.list(params),
        dataUpdatedAt: query.dataUpdatedAt,
        errorUpdatedAt: query.errorUpdatedAt,
    };
}

// Hook for debounced search
export function useUsersSearch(options: UseUsersQueryOptions & { debounceMs?: number } = {}) {
    const { debounceMs = 300, ...queryOptions } = options;

    // Simple debounce implementation
    const [debouncedSearch, setDebouncedSearch] = useState(queryOptions.search || '');

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearch(queryOptions.search || '');
        }, debounceMs);

        return () => clearTimeout(timer);
    }, [queryOptions.search, debounceMs]);

    return useUsersQuery({
        ...queryOptions,
        search: debouncedSearch,
    });
}
