import { useState, useEffect, useCallback, useRef } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';

interface ProgressiveLoadingOptions<T> {
    queryKey: string[];
    queryFn: (params: any) => Promise<{ data: T[]; pagination: any; total: number }>;
    initialParams?: any;
    pageSize?: number;
    cacheTime?: number;
    staleTime?: number;
    enableInfiniteScroll?: boolean;
    preloadNextPage?: boolean;
    maxCachePages?: number;
}

interface ProgressiveLoadingReturn<T> {
    data: T[];
    pagination: any;
    isLoading: boolean;
    isFetching: boolean;
    isError: boolean;
    error: any;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    currentPage: number;
    totalPages: number;
    totalItems: number;
    loadNextPage: () => void;
    loadPreviousPage: () => void;
    goToPage: (page: number) => void;
    refresh: () => void;
    prefetchNextPage: () => void;
    prefetchPreviousPage: () => void;
    clearCache: () => void;
    getCachedPages: () => number[];
}

export function useProgressiveLoading<T>({
    queryKey,
    queryFn,
    initialParams = {},
    pageSize = 50,
    cacheTime = 10 * 60 * 1000, // 10 minutes
    staleTime = 5 * 60 * 1000, // 5 minutes
    enableInfiniteScroll = false,
    preloadNextPage = true,
    maxCachePages = 10,
}: ProgressiveLoadingOptions<T>): ProgressiveLoadingReturn<T> {
    const [currentPage, setCurrentPage] = useState(1);
    const [allData, setAllData] = useState<T[]>([]);
    const queryClient = useQueryClient();
    const preloadTimeoutRef = useRef<NodeJS.Timeout>();

    // Build query parameters
    const queryParams = {
        ...initialParams,
        page: currentPage,
        per_page: pageSize,
    };

    // Main query for current page
    const {
        data: currentPageData,
        isLoading,
        isFetching,
        isError,
        error,
        refetch,
    } = useQuery({
        queryKey: [...queryKey, queryParams],
        queryFn: () => queryFn(queryParams),
        staleTime,
        cacheTime,
        keepPreviousData: true,
        refetchOnWindowFocus: false,
    });

    // Update all data when current page data changes
    useEffect(() => {
        if (currentPageData?.data) {
            if (enableInfiniteScroll) {
                // Append new data for infinite scroll
                setAllData(prev => {
                    const newData = [...prev];
                    const startIndex = (currentPage - 1) * pageSize;
                    
                    // Replace or append data
                    currentPageData.data.forEach((item, index) => {
                        newData[startIndex + index] = item;
                    });
                    
                    return newData;
                });
            } else {
                // Replace data for pagination
                setAllData(currentPageData.data);
            }
        }
    }, [currentPageData, currentPage, pageSize, enableInfiniteScroll]);

    // Pagination info
    const pagination = currentPageData?.pagination;
    const totalItems = currentPageData?.total || 0;
    const totalPages = pagination?.last_page || Math.ceil(totalItems / pageSize);
    const hasNextPage = currentPage < totalPages;
    const hasPreviousPage = currentPage > 1;

    // Navigation functions
    const loadNextPage = useCallback(() => {
        if (hasNextPage) {
            setCurrentPage(prev => prev + 1);
        }
    }, [hasNextPage]);

    const loadPreviousPage = useCallback(() => {
        if (hasPreviousPage) {
            setCurrentPage(prev => prev - 1);
        }
    }, [hasPreviousPage]);

    const goToPage = useCallback((page: number) => {
        if (page >= 1 && page <= totalPages) {
            setCurrentPage(page);
        }
    }, [totalPages]);

    // Prefetch functions
    const prefetchNextPage = useCallback(() => {
        if (hasNextPage && preloadNextPage) {
            const nextPageParams = {
                ...initialParams,
                page: currentPage + 1,
                per_page: pageSize,
            };

            queryClient.prefetchQuery({
                queryKey: [...queryKey, nextPageParams],
                queryFn: () => queryFn(nextPageParams),
                staleTime,
                cacheTime,
            });
        }
    }, [hasNextPage, currentPage, queryKey, queryFn, initialParams, pageSize, queryClient, staleTime, cacheTime, preloadNextPage]);

    const prefetchPreviousPage = useCallback(() => {
        if (hasPreviousPage) {
            const prevPageParams = {
                ...initialParams,
                page: currentPage - 1,
                per_page: pageSize,
            };

            queryClient.prefetchQuery({
                queryKey: [...queryKey, prevPageParams],
                queryFn: () => queryFn(prevPageParams),
                staleTime,
                cacheTime,
            });
        }
    }, [hasPreviousPage, currentPage, queryKey, queryFn, initialParams, pageSize, queryClient, staleTime, cacheTime]);

    // Auto-prefetch next page
    useEffect(() => {
        if (preloadNextPage && hasNextPage && !isFetching) {
            // Delay prefetch to avoid overwhelming the server
            preloadTimeoutRef.current = setTimeout(() => {
                prefetchNextPage();
            }, 1000);
        }

        return () => {
            if (preloadTimeoutRef.current) {
                clearTimeout(preloadTimeoutRef.current);
            }
        };
    }, [prefetchNextPage, hasNextPage, isFetching, preloadNextPage]);

    // Cache management
    const getCachedPages = useCallback(() => {
        const cache = queryClient.getQueryCache();
        const cachedPages: number[] = [];

        cache.getAll().forEach(query => {
            if (query.queryKey[0] === queryKey[0]) {
                const params = query.queryKey[query.queryKey.length - 1] as any;
                if (params?.page) {
                    cachedPages.push(params.page);
                }
            }
        });

        return cachedPages.sort((a, b) => a - b);
    }, [queryClient, queryKey]);

    const clearCache = useCallback(() => {
        queryClient.removeQueries({ queryKey: [queryKey[0]] });
        setAllData([]);
    }, [queryClient, queryKey]);

    // Cleanup old cache entries
    useEffect(() => {
        const cachedPages = getCachedPages();
        if (cachedPages.length > maxCachePages) {
            // Remove oldest cache entries
            const pagesToRemove = cachedPages.slice(0, cachedPages.length - maxCachePages);
            pagesToRemove.forEach(page => {
                const pageParams = {
                    ...initialParams,
                    page,
                    per_page: pageSize,
                };
                queryClient.removeQueries({ queryKey: [...queryKey, pageParams] });
            });
        }
    }, [currentPage, getCachedPages, maxCachePages, queryClient, queryKey, initialParams, pageSize]);

    const refresh = useCallback(() => {
        refetch();
    }, [refetch]);

    return {
        data: allData,
        pagination,
        isLoading,
        isFetching,
        isError,
        error,
        hasNextPage,
        hasPreviousPage,
        currentPage,
        totalPages,
        totalItems,
        loadNextPage,
        loadPreviousPage,
        goToPage,
        refresh,
        prefetchNextPage,
        prefetchPreviousPage,
        clearCache,
        getCachedPages,
    };
}
