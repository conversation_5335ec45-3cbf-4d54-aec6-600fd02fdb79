import { useState, useEffect, useCallback } from 'react';

interface FilterPersistenceOptions {
    key: string;
    defaultFilters?: any;
    storageType?: 'localStorage' | 'sessionStorage';
    expiration?: number; // in milliseconds
}

interface FilterPersistenceReturn<T> {
    filters: T;
    setFilters: (filters: T | ((prev: T) => T)) => void;
    clearFilters: () => void;
    resetToDefaults: () => void;
    isLoaded: boolean;
}

export function useFilterPersistence<T extends Record<string, any>>({
    key,
    defaultFilters = {} as T,
    storageType = 'localStorage',
    expiration = 24 * 60 * 60 * 1000, // 24 hours default
}: FilterPersistenceOptions): FilterPersistenceReturn<T> {
    const [filters, setFiltersState] = useState<T>(defaultFilters);
    const [isLoaded, setIsLoaded] = useState(false);

    const storage = storageType === 'localStorage' ? localStorage : sessionStorage;
    const storageKey = `filters_${key}`;

    // Load filters from storage on mount
    useEffect(() => {
        try {
            const stored = storage.getItem(storageKey);
            if (stored) {
                const parsed = JSON.parse(stored);
                
                // Check expiration
                if (parsed.timestamp && expiration) {
                    const now = Date.now();
                    if (now - parsed.timestamp > expiration) {
                        // Expired, remove from storage
                        storage.removeItem(storageKey);
                        setFiltersState(defaultFilters);
                        setIsLoaded(true);
                        return;
                    }
                }
                
                // Merge with defaults to ensure all required properties exist
                const mergedFilters = { ...defaultFilters, ...parsed.filters };
                setFiltersState(mergedFilters);
            }
        } catch (error) {
            console.warn('Failed to load filters from storage:', error);
            setFiltersState(defaultFilters);
        } finally {
            setIsLoaded(true);
        }
    }, [key, storageType, expiration]);

    // Save filters to storage whenever they change
    useEffect(() => {
        if (!isLoaded) return; // Don't save until initial load is complete

        try {
            const dataToStore = {
                filters,
                timestamp: Date.now(),
            };
            storage.setItem(storageKey, JSON.stringify(dataToStore));
        } catch (error) {
            console.warn('Failed to save filters to storage:', error);
        }
    }, [filters, isLoaded, storageKey, storage]);

    const setFilters = useCallback((newFilters: T | ((prev: T) => T)) => {
        if (typeof newFilters === 'function') {
            setFiltersState(prev => {
                const updated = newFilters(prev);
                return updated;
            });
        } else {
            setFiltersState(newFilters);
        }
    }, []);

    const clearFilters = useCallback(() => {
        setFiltersState({} as T);
        try {
            storage.removeItem(storageKey);
        } catch (error) {
            console.warn('Failed to clear filters from storage:', error);
        }
    }, [storageKey, storage]);

    const resetToDefaults = useCallback(() => {
        setFiltersState(defaultFilters);
    }, [defaultFilters]);

    return {
        filters,
        setFilters,
        clearFilters,
        resetToDefaults,
        isLoaded,
    };
}

// Hook for URL-based filter persistence (for sharing filtered views)
export function useUrlFilterPersistence<T extends Record<string, any>>(
    defaultFilters: T
): FilterPersistenceReturn<T> {
    const [filters, setFiltersState] = useState<T>(defaultFilters);
    const [isLoaded, setIsLoaded] = useState(false);

    // Load filters from URL on mount
    useEffect(() => {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const filtersParam = urlParams.get('filters');
            
            if (filtersParam) {
                const decoded = decodeURIComponent(filtersParam);
                const parsed = JSON.parse(decoded);
                const mergedFilters = { ...defaultFilters, ...parsed };
                setFiltersState(mergedFilters);
            }
        } catch (error) {
            console.warn('Failed to load filters from URL:', error);
            setFiltersState(defaultFilters);
        } finally {
            setIsLoaded(true);
        }
    }, []);

    // Update URL when filters change
    useEffect(() => {
        if (!isLoaded) return;

        try {
            const url = new URL(window.location.href);
            
            // Remove empty filters
            const cleanFilters = Object.fromEntries(
                Object.entries(filters).filter(([_, value]) => 
                    value !== undefined && value !== null && value !== ''
                )
            );

            if (Object.keys(cleanFilters).length > 0) {
                const encoded = encodeURIComponent(JSON.stringify(cleanFilters));
                url.searchParams.set('filters', encoded);
            } else {
                url.searchParams.delete('filters');
            }

            // Update URL without triggering navigation
            window.history.replaceState({}, '', url.toString());
        } catch (error) {
            console.warn('Failed to update URL with filters:', error);
        }
    }, [filters, isLoaded]);

    const setFilters = useCallback((newFilters: T | ((prev: T) => T)) => {
        if (typeof newFilters === 'function') {
            setFiltersState(prev => {
                const updated = newFilters(prev);
                return updated;
            });
        } else {
            setFiltersState(newFilters);
        }
    }, []);

    const clearFilters = useCallback(() => {
        setFiltersState(defaultFilters);
    }, [defaultFilters]);

    const resetToDefaults = useCallback(() => {
        setFiltersState(defaultFilters);
    }, [defaultFilters]);

    return {
        filters,
        setFilters,
        clearFilters,
        resetToDefaults,
        isLoaded,
    };
}

// Combined hook that uses both localStorage and URL persistence
export function useCombinedFilterPersistence<T extends Record<string, any>>(
    key: string,
    defaultFilters: T,
    options: {
        useUrl?: boolean;
        useStorage?: boolean;
        storageType?: 'localStorage' | 'sessionStorage';
        expiration?: number;
    } = {}
): FilterPersistenceReturn<T> {
    const {
        useUrl = false,
        useStorage = true,
        storageType = 'localStorage',
        expiration = 24 * 60 * 60 * 1000,
    } = options;

    const urlPersistence = useUrlFilterPersistence(defaultFilters);
    const storagePersistence = useFilterPersistence({
        key,
        defaultFilters,
        storageType,
        expiration,
    });

    if (useUrl && useStorage) {
        // Prioritize URL filters, fallback to storage
        const isLoaded = urlPersistence.isLoaded && storagePersistence.isLoaded;
        const hasUrlFilters = Object.keys(urlPersistence.filters).length > 0;
        
        return {
            filters: hasUrlFilters ? urlPersistence.filters : storagePersistence.filters,
            setFilters: (newFilters) => {
                urlPersistence.setFilters(newFilters);
                storagePersistence.setFilters(newFilters);
            },
            clearFilters: () => {
                urlPersistence.clearFilters();
                storagePersistence.clearFilters();
            },
            resetToDefaults: () => {
                urlPersistence.resetToDefaults();
                storagePersistence.resetToDefaults();
            },
            isLoaded,
        };
    } else if (useUrl) {
        return urlPersistence;
    } else {
        return storagePersistence;
    }
}
