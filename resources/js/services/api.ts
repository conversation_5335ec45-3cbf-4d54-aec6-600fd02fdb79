// Lightweight API client for backend endpoints
import type { User as AppUser } from '@/stores/app-store';

export interface Paginated<T> {
    data: T[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

export interface ApiUser {
    id: number;
    name: string;
    phone: string | null;
    gender: 'male' | 'female' | null;
    birth_date: string | null;
    address: string | null;
    facebook_url?: string | null;
    college: string | null;
    academic_year: 1 | 2 | 3 | 4 | null;
    department: string | null;
    first_attendance_date: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

export type GetUsersParams = {
    year?: number;
    search?: string;
    per_page?: number;
    page?: number;
    sort_by?: 'name' | 'academic_year' | 'created_at' | 'birth_date' | 'first_attendance_date';
    sort_dir?: 'asc' | 'desc';
    is_active?: boolean;
    department?: string;
    college?: string;
    gender?: 'male' | 'female';
    has_attendance?: boolean;
    refresh?: boolean;
};

export type UsersResponse = {
    users: Paginated<ApiUser>;
    summary: {
        total_users: number;
        active_users: number;
        filtered_count: number;
        users_with_qr: number;
        by_year: Record<string, number>;
        by_gender: Record<string, number>;
    };
    filter_options: {
        departments: string[];
        colleges: string[];
        years: number[];
        genders: string[];
    };
    query_info: {
        total_queries: number;
        cache_hit: boolean;
    };
};

// Query keys for TanStack Query
export const userQueryKeys = {
    all: ['users'] as const,
    lists: () => [...userQueryKeys.all, 'list'] as const,
    list: (params: GetUsersParams) => [...userQueryKeys.lists(), params] as const,
    details: () => [...userQueryKeys.all, 'detail'] as const,
    detail: (id: string) => [...userQueryKeys.details(), id] as const,
};

function mapApiUserToAppUser(u: ApiUser): AppUser {
    return {
        id: String(u.id),
        name: u.name,
        phone: u.phone || '',
        gender: (u.gender as 'male' | 'female') || 'male',
        year: (u.academic_year as 1 | 2 | 3 | 4) || 1,
        college: u.college || '',
        department: u.department || '',
        birthdate: u.birth_date || new Date().toISOString(),
        address: u.address || '',
        facebook_url: u.facebook_url || undefined,
        first_attendance_date: u.first_attendance_date || new Date().toISOString(),
        qr_code: undefined,
        created_at: u.created_at,
        updated_at: u.updated_at,
    };
}

class ApiService {
    private baseURL = '/api';

    private buildHeaders() {
        const headers: Record<string, string> = { Accept: 'application/json' };
        // Optional: support bearer token if present (fallback to Sanctum cookies)
        const token = localStorage.getItem('api-token');
        if (token) headers['Authorization'] = `Bearer ${token}`;
        return headers;
    }

    private async handleResponse(res: Response) {
        if (res.ok) return res.json();
        // Try to parse JSON error if available
        let message = `HTTP ${res.status}`;
        try {
            const err = await res.json();
            message = err.message || JSON.stringify(err);
        } catch (_) {
            const text = await res.text();
            if (text) message = text;
        }
        if (res.status === 401) throw new Error('Unauthorized. Please log in.');
        if (res.status === 422) throw new Error(`Validation error: ${message}`);
        throw new Error(message);
    }

    async getUsers(params: GetUsersParams = {}): Promise<UsersResponse> {
        // Ensure per_page doesn't exceed backend limit
        const safeParams = {
            ...params,
            per_page: Math.min(params.per_page || 50, 100),
        };

        const qs = new URLSearchParams();
        Object.entries(safeParams).forEach(([k, v]) => {
            if (v !== undefined && v !== null && v !== '') {
                // Handle boolean values properly for Laravel validation
                if (typeof v === 'boolean') {
                    qs.append(k, v ? '1' : '0');
                } else {
                    qs.append(k, String(v));
                }
            }
        });

        const res = await fetch(`${this.baseURL}/users?${qs.toString()}`, {
            headers: this.buildHeaders(),
            credentials: 'include',
        });

        return this.handleResponse(res);
    }

    // Convenience method to return mapped UI data
    async getUsersForUI(params: GetUsersParams = {}): Promise<{
        data: AppUser[];
        pagination: Omit<Paginated<ApiUser>, 'data'>;
        summary: any;
        filter_options: any;
        query_info: any;
    }> {
        const { users, summary, filter_options, query_info } = await this.getUsers(params);
        const mapped = users.data.map(mapApiUserToAppUser);
        const { data: _ignored, ...pagination } = users;
        return { data: mapped, pagination, summary, filter_options, query_info };
    }
}

export const apiService = new ApiService();
