import React from 'react';
import {
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
    MoreHorizontal,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface PaginationInfo {
    pageIndex: number;
    pageSize: number;
    pageCount: number;
    total: number;
}

interface EnhancedPaginationProps {
    pagination: PaginationInfo;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
    className?: string;
    isRTL?: boolean;
    showPageSizeSelector?: boolean;
    showPageInfo?: boolean;
    showQuickJumper?: boolean;
}

export function EnhancedPagination({
    pagination,
    onPageChange,
    onPageSizeChange,
    className,
    isRTL = true,
    showPageSizeSelector = true,
    showPageInfo = true,
    showQuickJumper = false,
}: EnhancedPaginationProps) {
    const { pageIndex, pageSize, pageCount, total } = pagination;
    const currentPage = pageIndex + 1; // Convert from 0-based to 1-based

    // Calculate visible page numbers
    const getVisiblePages = () => {
        const delta = 2;
        const range = [];
        const rangeWithDots = [];

        for (let i = Math.max(2, currentPage - delta); i <= Math.min(pageCount - 1, currentPage + delta); i++) {
            range.push(i);
        }

        if (currentPage - delta > 2) {
            rangeWithDots.push(1, '...');
        } else {
            rangeWithDots.push(1);
        }

        rangeWithDots.push(...range);

        if (currentPage + delta < pageCount - 1) {
            rangeWithDots.push('...', pageCount);
        } else {
            rangeWithDots.push(pageCount);
        }

        return rangeWithDots;
    };

    const visiblePages = pageCount > 1 ? getVisiblePages() : [1];

    const startItem = pageIndex * pageSize + 1;
    const endItem = Math.min((pageIndex + 1) * pageSize, total);

    return (
        <div className={cn(
            'flex items-center justify-between space-x-2',
            isRTL && 'flex-row-reverse space-x-reverse',
            className
        )}>
            {/* Page Info */}
            {showPageInfo && (
                <div className="text-sm text-muted-foreground">
                    عرض {startItem.toLocaleString('ar')} إلى {endItem.toLocaleString('ar')} من {total.toLocaleString('ar')} نتيجة
                </div>
            )}

            {/* Pagination Controls */}
            <div className={cn(
                'flex items-center space-x-2',
                isRTL && 'flex-row-reverse space-x-reverse'
            )}>
                {/* Page Size Selector */}
                {showPageSizeSelector && (
                    <div className={cn(
                        'flex items-center space-x-2',
                        isRTL && 'flex-row-reverse space-x-reverse'
                    )}>
                        <span className="text-sm text-muted-foreground">عرض</span>
                        <Select
                            value={pageSize.toString()}
                            onValueChange={(value) => onPageSizeChange(Number(value))}
                        >
                            <SelectTrigger className="w-20">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="25">25</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectContent>
                        </Select>
                        <span className="text-sm text-muted-foreground">عنصر</span>
                    </div>
                )}

                {/* Navigation Buttons */}
                <div className={cn(
                    'flex items-center space-x-1',
                    isRTL && 'flex-row-reverse space-x-reverse'
                )}>
                    {/* First Page */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onPageChange(1)}
                        disabled={currentPage === 1}
                        className="h-8 w-8 p-0"
                    >
                        <ChevronsRight className="h-4 w-4" />
                    </Button>

                    {/* Previous Page */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onPageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="h-8 w-8 p-0"
                    >
                        <ChevronRight className="h-4 w-4" />
                    </Button>

                    {/* Page Numbers */}
                    {visiblePages.map((page, index) => (
                        <React.Fragment key={index}>
                            {page === '...' ? (
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    disabled
                                    className="h-8 w-8 p-0"
                                >
                                    <MoreHorizontal className="h-4 w-4" />
                                </Button>
                            ) : (
                                <Button
                                    variant={currentPage === page ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => onPageChange(page as number)}
                                    className="h-8 w-8 p-0"
                                >
                                    {(page as number).toLocaleString('ar')}
                                </Button>
                            )}
                        </React.Fragment>
                    ))}

                    {/* Next Page */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onPageChange(currentPage + 1)}
                        disabled={currentPage === pageCount}
                        className="h-8 w-8 p-0"
                    >
                        <ChevronLeft className="h-4 w-4" />
                    </Button>

                    {/* Last Page */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onPageChange(pageCount)}
                        disabled={currentPage === pageCount}
                        className="h-8 w-8 p-0"
                    >
                        <ChevronsLeft className="h-4 w-4" />
                    </Button>
                </div>

                {/* Quick Jumper */}
                {showQuickJumper && (
                    <div className={cn(
                        'flex items-center space-x-2',
                        isRTL && 'flex-row-reverse space-x-reverse'
                    )}>
                        <span className="text-sm text-muted-foreground">الذهاب إلى</span>
                        <input
                            type="number"
                            min={1}
                            max={pageCount}
                            className="w-16 h-8 px-2 text-sm border rounded"
                            onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                    const value = parseInt((e.target as HTMLInputElement).value);
                                    if (value >= 1 && value <= pageCount) {
                                        onPageChange(value);
                                    }
                                }
                            }}
                        />
                    </div>
                )}
            </div>
        </div>
    );
}
