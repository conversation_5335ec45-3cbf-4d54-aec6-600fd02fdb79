import React, { useState, useEffect, useCallback } from 'react';
import { Search, Filter, X, ChevronDown, SlidersHorizontal } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

export interface SearchFilters {
    search: string;
    department?: string;
    college?: string;
    gender?: 'male' | 'female';
    hasAttendance?: boolean;
    isActive?: boolean;
}

export interface FilterOptions {
    departments: string[];
    colleges: string[];
    years: number[];
    genders: string[];
}

interface AdvancedSearchProps {
    filters: SearchFilters;
    onFiltersChange: (filters: SearchFilters) => void;
    filterOptions?: FilterOptions;
    placeholder?: string;
    isSearching?: boolean;
    className?: string;
    isRTL?: boolean;
}

export function AdvancedSearch({
    filters,
    onFiltersChange,
    filterOptions,
    placeholder = "البحث في الطلاب...",
    isSearching = false,
    className,
    isRTL = true,
}: AdvancedSearchProps) {
    const [searchValue, setSearchValue] = useState(filters.search);
    const [showFilters, setShowFilters] = useState(false);

    // Debounced search
    useEffect(() => {
        const timer = setTimeout(() => {
            if (searchValue !== filters.search) {
                onFiltersChange({ ...filters, search: searchValue });
            }
        }, 300);

        return () => clearTimeout(timer);
    }, [searchValue, filters, onFiltersChange]);

    const handleFilterChange = useCallback((key: keyof SearchFilters, value: any) => {
        onFiltersChange({ ...filters, [key]: value });
    }, [filters, onFiltersChange]);

    const clearFilter = useCallback((key: keyof SearchFilters) => {
        const newFilters = { ...filters };
        delete newFilters[key];
        onFiltersChange(newFilters);
    }, [filters, onFiltersChange]);

    const clearAllFilters = useCallback(() => {
        onFiltersChange({ search: '' });
        setSearchValue('');
    }, [onFiltersChange]);

    const activeFiltersCount = Object.keys(filters).filter(
        key => key !== 'search' && filters[key as keyof SearchFilters] !== undefined
    ).length;

    const hasActiveFilters = activeFiltersCount > 0 || filters.search.length > 0;

    return (
        <div className={cn('space-y-4', className)}>
            {/* Search Input */}
            <div className="relative">
                <Search className={cn(
                    'absolute top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground',
                    isRTL ? 'right-3' : 'left-3'
                )} />
                <Input
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    placeholder={placeholder}
                    className={cn(
                        'h-11',
                        isRTL ? 'pr-10 pl-12' : 'pl-10 pr-12'
                    )}
                    dir={isRTL ? 'rtl' : 'ltr'}
                />
                {isSearching && (
                    <div className={cn(
                        'absolute top-1/2 -translate-y-1/2',
                        isRTL ? 'left-3' : 'right-3'
                    )}>
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                    </div>
                )}
                {!isSearching && searchValue && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                            setSearchValue('');
                            onFiltersChange({ ...filters, search: '' });
                        }}
                        className={cn(
                            'absolute top-1/2 h-6 w-6 -translate-y-1/2 p-0',
                            isRTL ? 'left-2' : 'right-2'
                        )}
                    >
                        <X className="h-3 w-3" />
                    </Button>
                )}
            </div>

            {/* Filter Controls */}
            <div className={cn(
                'flex items-center gap-2',
                isRTL ? 'flex-row-reverse' : 'flex-row'
            )}>
                {/* Advanced Filters Toggle */}
                <Popover open={showFilters} onOpenChange={setShowFilters}>
                    <PopoverTrigger asChild>
                        <Button
                            variant="outline"
                            size="sm"
                            className={cn(
                                'gap-2',
                                activeFiltersCount > 0 && 'border-primary bg-primary/5'
                            )}
                        >
                            <SlidersHorizontal className="h-4 w-4" />
                            فلاتر متقدمة
                            {activeFiltersCount > 0 && (
                                <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                                    {activeFiltersCount}
                                </Badge>
                            )}
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80" align={isRTL ? 'end' : 'start'}>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <h4 className="font-medium">الفلاتر المتقدمة</h4>
                                {hasActiveFilters && (
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={clearAllFilters}
                                        className="h-auto p-1 text-xs"
                                    >
                                        مسح الكل
                                    </Button>
                                )}
                            </div>

                            {/* Department Filter */}
                            {filterOptions?.departments && filterOptions.departments.length > 0 && (
                                <div className="space-y-2">
                                    <label className="text-sm font-medium">القسم</label>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="outline" className="w-full justify-between">
                                                {filters.department || 'اختر القسم'}
                                                <ChevronDown className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent className="w-full">
                                            <DropdownMenuItem
                                                onClick={() => clearFilter('department')}
                                            >
                                                جميع الأقسام
                                            </DropdownMenuItem>
                                            <DropdownMenuSeparator />
                                            {filterOptions.departments.map((dept) => (
                                                <DropdownMenuItem
                                                    key={dept}
                                                    onClick={() => handleFilterChange('department', dept)}
                                                >
                                                    {dept}
                                                </DropdownMenuItem>
                                            ))}
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            )}

                            {/* College Filter */}
                            {filterOptions?.colleges && filterOptions.colleges.length > 0 && (
                                <div className="space-y-2">
                                    <label className="text-sm font-medium">الكلية</label>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="outline" className="w-full justify-between">
                                                {filters.college || 'اختر الكلية'}
                                                <ChevronDown className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent className="w-full">
                                            <DropdownMenuItem
                                                onClick={() => clearFilter('college')}
                                            >
                                                جميع الكليات
                                            </DropdownMenuItem>
                                            <DropdownMenuSeparator />
                                            {filterOptions.colleges.map((college) => (
                                                <DropdownMenuItem
                                                    key={college}
                                                    onClick={() => handleFilterChange('college', college)}
                                                >
                                                    {college}
                                                </DropdownMenuItem>
                                            ))}
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            )}

                            {/* Gender Filter */}
                            <div className="space-y-2">
                                <label className="text-sm font-medium">الجنس</label>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="outline" className="w-full justify-between">
                                            {filters.gender === 'male' ? 'ذكر' : 
                                             filters.gender === 'female' ? 'أنثى' : 'الكل'}
                                            <ChevronDown className="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>
                                        <DropdownMenuItem onClick={() => clearFilter('gender')}>
                                            الكل
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleFilterChange('gender', 'male')}>
                                            ذكر
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleFilterChange('gender', 'female')}>
                                            أنثى
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>

                            {/* Attendance Filter */}
                            <div className="space-y-2">
                                <label className="text-sm font-medium">حالة الحضور</label>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="outline" className="w-full justify-between">
                                            {filters.hasAttendance === true ? 'لديه حضور' : 
                                             filters.hasAttendance === false ? 'لا يوجد حضور' : 'الكل'}
                                            <ChevronDown className="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>
                                        <DropdownMenuItem onClick={() => clearFilter('hasAttendance')}>
                                            الكل
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleFilterChange('hasAttendance', true)}>
                                            لديه حضور
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleFilterChange('hasAttendance', false)}>
                                            لا يوجد حضور
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>

                {/* Active Filter Chips */}
                {hasActiveFilters && (
                    <div className={cn(
                        'flex flex-wrap gap-1',
                        isRTL ? 'flex-row-reverse' : 'flex-row'
                    )}>
                        {filters.search && (
                            <Badge variant="secondary" className="gap-1">
                                بحث: {filters.search}
                                <X 
                                    className="h-3 w-3 cursor-pointer" 
                                    onClick={() => {
                                        setSearchValue('');
                                        clearFilter('search');
                                    }}
                                />
                            </Badge>
                        )}
                        {filters.department && (
                            <Badge variant="secondary" className="gap-1">
                                قسم: {filters.department}
                                <X 
                                    className="h-3 w-3 cursor-pointer" 
                                    onClick={() => clearFilter('department')}
                                />
                            </Badge>
                        )}
                        {filters.college && (
                            <Badge variant="secondary" className="gap-1">
                                كلية: {filters.college}
                                <X 
                                    className="h-3 w-3 cursor-pointer" 
                                    onClick={() => clearFilter('college')}
                                />
                            </Badge>
                        )}
                        {filters.gender && (
                            <Badge variant="secondary" className="gap-1">
                                {filters.gender === 'male' ? 'ذكر' : 'أنثى'}
                                <X 
                                    className="h-3 w-3 cursor-pointer" 
                                    onClick={() => clearFilter('gender')}
                                />
                            </Badge>
                        )}
                        {filters.hasAttendance !== undefined && (
                            <Badge variant="secondary" className="gap-1">
                                {filters.hasAttendance ? 'لديه حضور' : 'لا يوجد حضور'}
                                <X 
                                    className="h-3 w-3 cursor-pointer" 
                                    onClick={() => clearFilter('hasAttendance')}
                                />
                            </Badge>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}
