import React, { useState, useEffect } from 'react';
import { ChevronDown, X, Filter, Calendar, Users, GraduationCap, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

interface FilterOption {
    value: string;
    label: string;
    count?: number;
}

interface DateRange {
    from?: Date;
    to?: Date;
}

interface AdvancedFiltersProps {
    filters: {
        department?: string;
        college?: string;
        gender?: 'male' | 'female';
        hasAttendance?: boolean;
        isActive?: boolean;
        dateRange?: DateRange;
        registrationDateRange?: DateRange;
    };
    filterOptions?: {
        departments?: FilterOption[];
        colleges?: FilterOption[];
    };
    onFiltersChange: (filters: any) => void;
    onClearAll: () => void;
    className?: string;
    isRTL?: boolean;
    showAdvanced?: boolean;
}

export function AdvancedFilters({
    filters,
    filterOptions,
    onFiltersChange,
    onClearAll,
    className,
    isRTL = true,
    showAdvanced = false,
}: AdvancedFiltersProps) {
    const [isAdvancedOpen, setIsAdvancedOpen] = useState(showAdvanced);
    const [activeFiltersCount, setActiveFiltersCount] = useState(0);

    // Count active filters
    useEffect(() => {
        const count = Object.values(filters).filter(value => 
            value !== undefined && value !== null && value !== ''
        ).length;
        setActiveFiltersCount(count);
    }, [filters]);

    const handleFilterChange = (key: string, value: any) => {
        onFiltersChange({
            ...filters,
            [key]: value,
        });
    };

    const removeFilter = (key: string) => {
        const newFilters = { ...filters };
        delete newFilters[key];
        onFiltersChange(newFilters);
    };

    const hasActiveFilters = activeFiltersCount > 0;

    return (
        <div className={cn('space-y-4', className)}>
            {/* Filter Controls */}
            <div className={cn(
                'flex flex-wrap items-center gap-2',
                isRTL ? 'flex-row-reverse' : 'flex-row'
            )}>
                {/* Department Filter */}
                {filterOptions?.departments && filterOptions.departments.length > 0 && (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button 
                                variant={filters.department ? "default" : "outline"} 
                                size="sm"
                                className={cn(
                                    'gap-2',
                                    filters.department && 'bg-primary text-primary-foreground'
                                )}
                            >
                                <GraduationCap className="h-4 w-4" />
                                {filters.department || 'القسم'}
                                <ChevronDown className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align={isRTL ? 'end' : 'start'}>
                            <DropdownMenuLabel>اختر القسم</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => removeFilter('department')}>
                                جميع الأقسام
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {filterOptions.departments.map((dept) => (
                                <DropdownMenuItem 
                                    key={dept.value} 
                                    onClick={() => handleFilterChange('department', dept.value)}
                                    className={cn(
                                        filters.department === dept.value && 'bg-primary/10'
                                    )}
                                >
                                    <div className="flex items-center justify-between w-full">
                                        <span>{dept.label}</span>
                                        {dept.count && (
                                            <Badge variant="secondary" className="text-xs">
                                                {dept.count}
                                            </Badge>
                                        )}
                                    </div>
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                )}

                {/* College Filter */}
                {filterOptions?.colleges && filterOptions.colleges.length > 0 && (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button 
                                variant={filters.college ? "default" : "outline"} 
                                size="sm"
                                className={cn(
                                    'gap-2',
                                    filters.college && 'bg-primary text-primary-foreground'
                                )}
                            >
                                <GraduationCap className="h-4 w-4" />
                                {filters.college || 'الكلية'}
                                <ChevronDown className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align={isRTL ? 'end' : 'start'}>
                            <DropdownMenuLabel>اختر الكلية</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => removeFilter('college')}>
                                جميع الكليات
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {filterOptions.colleges.map((college) => (
                                <DropdownMenuItem 
                                    key={college.value} 
                                    onClick={() => handleFilterChange('college', college.value)}
                                    className={cn(
                                        filters.college === college.value && 'bg-primary/10'
                                    )}
                                >
                                    <div className="flex items-center justify-between w-full">
                                        <span>{college.label}</span>
                                        {college.count && (
                                            <Badge variant="secondary" className="text-xs">
                                                {college.count}
                                            </Badge>
                                        )}
                                    </div>
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                )}

                {/* Gender Filter */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button 
                            variant={filters.gender ? "default" : "outline"} 
                            size="sm"
                            className={cn(
                                'gap-2',
                                filters.gender && 'bg-primary text-primary-foreground'
                            )}
                        >
                            <Users className="h-4 w-4" />
                            {filters.gender === 'male' ? 'ذكر' : filters.gender === 'female' ? 'أنثى' : 'الجنس'}
                            <ChevronDown className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align={isRTL ? 'end' : 'start'}>
                        <DropdownMenuLabel>اختر الجنس</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => removeFilter('gender')}>
                            الكل
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                            onClick={() => handleFilterChange('gender', 'male')}
                            className={cn(filters.gender === 'male' && 'bg-primary/10')}
                        >
                            ذكر
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                            onClick={() => handleFilterChange('gender', 'female')}
                            className={cn(filters.gender === 'female' && 'bg-primary/10')}
                        >
                            أنثى
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Attendance Status Filter */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button 
                            variant={filters.hasAttendance !== undefined ? "default" : "outline"} 
                            size="sm"
                            className={cn(
                                'gap-2',
                                filters.hasAttendance !== undefined && 'bg-primary text-primary-foreground'
                            )}
                        >
                            <Calendar className="h-4 w-4" />
                            {filters.hasAttendance === true ? 'لديه حضور' : 
                             filters.hasAttendance === false ? 'بدون حضور' : 'حالة الحضور'}
                            <ChevronDown className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align={isRTL ? 'end' : 'start'}>
                        <DropdownMenuLabel>حالة الحضور</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => removeFilter('hasAttendance')}>
                            الكل
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                            onClick={() => handleFilterChange('hasAttendance', true)}
                            className={cn(filters.hasAttendance === true && 'bg-primary/10')}
                        >
                            لديه حضور
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                            onClick={() => handleFilterChange('hasAttendance', false)}
                            className={cn(filters.hasAttendance === false && 'bg-primary/10')}
                        >
                            بدون حضور
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Advanced Filters Toggle */}
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
                    className="gap-2"
                >
                    <Filter className="h-4 w-4" />
                    فلاتر متقدمة
                    <ChevronDown className={cn(
                        'h-4 w-4 transition-transform',
                        isAdvancedOpen && 'rotate-180'
                    )} />
                </Button>

                {/* Clear All Filters */}
                {hasActiveFilters && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={onClearAll}
                        className="gap-2 text-destructive hover:text-destructive"
                    >
                        <RotateCcw className="h-4 w-4" />
                        مسح الكل
                    </Button>
                )}

                {/* Active Filters Count */}
                {hasActiveFilters && (
                    <Badge variant="secondary" className="gap-1">
                        <Filter className="h-3 w-3" />
                        {activeFiltersCount} فلتر نشط
                    </Badge>
                )}
            </div>

            {/* Active Filter Chips */}
            {hasActiveFilters && (
                <div className={cn(
                    'flex flex-wrap gap-2',
                    isRTL && 'flex-row-reverse'
                )}>
                    {filters.department && (
                        <Badge variant="secondary" className="gap-1">
                            قسم: {filters.department}
                            <X 
                                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                                onClick={() => removeFilter('department')}
                            />
                        </Badge>
                    )}
                    {filters.college && (
                        <Badge variant="secondary" className="gap-1">
                            كلية: {filters.college}
                            <X 
                                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                                onClick={() => removeFilter('college')}
                            />
                        </Badge>
                    )}
                    {filters.gender && (
                        <Badge variant="secondary" className="gap-1">
                            {filters.gender === 'male' ? 'ذكر' : 'أنثى'}
                            <X 
                                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                                onClick={() => removeFilter('gender')}
                            />
                        </Badge>
                    )}
                    {filters.hasAttendance !== undefined && (
                        <Badge variant="secondary" className="gap-1">
                            {filters.hasAttendance ? 'لديه حضور' : 'بدون حضور'}
                            <X 
                                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                                onClick={() => removeFilter('hasAttendance')}
                            />
                        </Badge>
                    )}
                </div>
            )}

            {/* Advanced Filters Panel */}
            {isAdvancedOpen && (
                <Card className="border-dashed">
                    <CardContent className="p-4">
                        <div className="space-y-4">
                            <h4 className="font-medium text-sm">فلاتر متقدمة</h4>
                            
                            {/* Date Range Filters */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="text-sm font-medium mb-2 block">تاريخ التسجيل</label>
                                    <Button variant="outline" size="sm" className="w-full justify-start">
                                        <Calendar className="h-4 w-4 mr-2" />
                                        اختر التاريخ
                                    </Button>
                                </div>
                                <div>
                                    <label className="text-sm font-medium mb-2 block">تاريخ أول حضور</label>
                                    <Button variant="outline" size="sm" className="w-full justify-start">
                                        <Calendar className="h-4 w-4 mr-2" />
                                        اختر التاريخ
                                    </Button>
                                </div>
                            </div>

                            {/* Status Filters */}
                            <div>
                                <label className="text-sm font-medium mb-2 block">حالة الطالب</label>
                                <div className="flex gap-2">
                                    <Button
                                        variant={filters.isActive === true ? "default" : "outline"}
                                        size="sm"
                                        onClick={() => handleFilterChange('isActive', true)}
                                    >
                                        نشط
                                    </Button>
                                    <Button
                                        variant={filters.isActive === false ? "default" : "outline"}
                                        size="sm"
                                        onClick={() => handleFilterChange('isActive', false)}
                                    >
                                        غير نشط
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeFilter('isActive')}
                                    >
                                        الكل
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
