import React, { useMemo, useRef, useEffect, useState, useCallback } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { flexRender, type Table as TanStackTable } from '@tanstack/react-table';
import { cn } from '@/lib/utils';

interface VirtualTableProps<T> {
    table: TanStackTable<T>;
    data: T[];
    className?: string;
    estimateSize?: number;
    overscan?: number;
    enableVirtualization?: boolean;
    onScroll?: (scrollTop: number) => void;
    isLoading?: boolean;
    loadingRows?: number;
}

export function VirtualTable<T>({
    table,
    data,
    className,
    estimateSize = 60,
    overscan = 5,
    enableVirtualization = true,
    onScroll,
    isLoading = false,
    loadingRows = 10,
}: VirtualTableProps<T>) {
    const tableContainerRef = useRef<HTMLDivElement>(null);
    const [containerHeight, setContainerHeight] = useState(600);

    // Measure container height
    useEffect(() => {
        const container = tableContainerRef.current;
        if (!container) return;

        const resizeObserver = new ResizeObserver((entries) => {
            for (const entry of entries) {
                setContainerHeight(entry.contentRect.height);
            }
        });

        resizeObserver.observe(container);
        return () => resizeObserver.disconnect();
    }, []);

    // Get rows to render
    const rows = useMemo(() => {
        if (isLoading) {
            // Create skeleton rows for loading state
            return Array.from({ length: loadingRows }, (_, index) => ({
                id: `loading-${index}`,
                isLoading: true,
            }));
        }
        return table.getRowModel().rows;
    }, [table, isLoading, loadingRows]);

    // Virtual scrolling setup
    const virtualizer = useVirtualizer({
        count: rows.length,
        getScrollElement: () => tableContainerRef.current,
        estimateSize: () => estimateSize,
        overscan,
        enabled: enableVirtualization && rows.length > 50, // Only enable for large datasets
    });

    // Handle scroll events
    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
        const scrollTop = e.currentTarget.scrollTop;
        onScroll?.(scrollTop);
    }, [onScroll]);

    // Get virtual items
    const virtualItems = virtualizer.getVirtualItems();

    if (!enableVirtualization || rows.length <= 50) {
        // Render normal table for small datasets
        return (
            <div className={cn('rounded-md border', className)}>
                <table className="w-full">
                    <thead className="bg-muted/50">
                        {table.getHeaderGroups().map((headerGroup) => (
                            <tr key={headerGroup.id} className="border-b">
                                {headerGroup.headers.map((header) => (
                                    <th
                                        key={header.id}
                                        className="h-12 px-4 text-left align-middle font-medium text-muted-foreground"
                                        style={{ width: header.getSize() }}
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(header.column.columnDef.header, header.getContext())}
                                    </th>
                                ))}
                            </tr>
                        ))}
                    </thead>
                    <tbody>
                        {rows.map((row) => (
                            <tr
                                key={row.id}
                                className="border-b transition-colors hover:bg-muted/50"
                            >
                                {isLoading ? (
                                    // Skeleton row
                                    table.getAllColumns().map((column, index) => (
                                        <td key={index} className="p-4">
                                            <div className="h-4 w-full animate-pulse rounded bg-muted" />
                                        </td>
                                    ))
                                ) : (
                                    row.getVisibleCells().map((cell) => (
                                        <td key={cell.id} className="p-4 align-middle">
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </td>
                                    ))
                                )}
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        );
    }

    // Render virtualized table
    return (
        <div className={cn('rounded-md border', className)}>
            {/* Header */}
            <div className="bg-muted/50 border-b">
                <table className="w-full">
                    <thead>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <tr key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <th
                                        key={header.id}
                                        className="h-12 px-4 text-left align-middle font-medium text-muted-foreground"
                                        style={{ width: header.getSize() }}
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(header.column.columnDef.header, header.getContext())}
                                    </th>
                                ))}
                            </tr>
                        ))}
                    </thead>
                </table>
            </div>

            {/* Virtualized Body */}
            <div
                ref={tableContainerRef}
                className="relative overflow-auto"
                style={{ height: `${Math.min(containerHeight, 600)}px` }}
                onScroll={handleScroll}
            >
                <div
                    style={{
                        height: `${virtualizer.getTotalSize()}px`,
                        width: '100%',
                        position: 'relative',
                    }}
                >
                    {virtualItems.map((virtualItem) => {
                        const row = rows[virtualItem.index];
                        if (!row) return null;

                        return (
                            <div
                                key={virtualItem.key}
                                style={{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: '100%',
                                    height: `${virtualItem.size}px`,
                                    transform: `translateY(${virtualItem.start}px)`,
                                }}
                            >
                                <table className="w-full">
                                    <tbody>
                                        <tr className="border-b transition-colors hover:bg-muted/50">
                                            {isLoading ? (
                                                // Skeleton row
                                                table.getAllColumns().map((column, index) => (
                                                    <td key={index} className="p-4">
                                                        <div className="h-4 w-full animate-pulse rounded bg-muted" />
                                                    </td>
                                                ))
                                            ) : (
                                                row.getVisibleCells().map((cell) => (
                                                    <td key={cell.id} className="p-4 align-middle">
                                                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                                    </td>
                                                ))
                                            )}
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        );
                    })}
                </div>
            </div>

            {/* Loading indicator for virtual scrolling */}
            {isLoading && (
                <div className="flex items-center justify-center p-4 border-t">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                        <span>تحميل المزيد من البيانات...</span>
                    </div>
                </div>
            )}
        </div>
    );
}
