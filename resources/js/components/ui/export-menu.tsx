import React, { useState } from 'react';
import {
    Download,
    FileText,
    FileSpreadsheet,
    FileImage,
    Printer,
    Settings,
    Check,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    DropdownMenuCheckboxItem,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface ExportOptions {
    format: 'csv' | 'excel' | 'pdf' | 'json';
    includeHeaders: boolean;
    selectedOnly: boolean;
    columns: string[];
    dateRange?: {
        start: Date;
        end: Date;
    };
}

interface ExportMenuProps {
    onExport: (options: ExportOptions) => void;
    selectedCount?: number;
    totalCount?: number;
    availableColumns?: Array<{ key: string; label: string }>;
    className?: string;
    isRTL?: boolean;
    disabled?: boolean;
}

export function ExportMenu({
    onExport,
    selectedCount = 0,
    totalCount = 0,
    availableColumns = [],
    className,
    isRTL = true,
    disabled = false,
}: ExportMenuProps) {
    const [exportOptions, setExportOptions] = useState<ExportOptions>({
        format: 'csv',
        includeHeaders: true,
        selectedOnly: false,
        columns: availableColumns.map(col => col.key),
    });

    const formatIcons = {
        csv: FileSpreadsheet,
        excel: FileSpreadsheet,
        pdf: FileText,
        json: FileText,
    };

    const formatLabels = {
        csv: 'CSV',
        excel: 'Excel',
        pdf: 'PDF',
        json: 'JSON',
    };

    const handleExport = (format: ExportOptions['format']) => {
        const options = { ...exportOptions, format };
        onExport(options);
    };

    const toggleColumn = (columnKey: string) => {
        setExportOptions(prev => ({
            ...prev,
            columns: prev.columns.includes(columnKey)
                ? prev.columns.filter(col => col !== columnKey)
                : [...prev.columns, columnKey]
        }));
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="outline"
                    size="sm"
                    disabled={disabled}
                    className={cn('gap-2', className)}
                >
                    <Download className="h-4 w-4" />
                    تصدير
                    {selectedCount > 0 && (
                        <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                            {selectedCount}
                        </Badge>
                    )}
                </Button>
            </DropdownMenuTrigger>
            
            <DropdownMenuContent className="w-64" align={isRTL ? 'end' : 'start'}>
                <DropdownMenuLabel className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    خيارات التصدير
                </DropdownMenuLabel>
                <DropdownMenuSeparator />

                {/* Export Formats */}
                <DropdownMenuLabel className="text-xs text-muted-foreground">
                    تنسيق الملف
                </DropdownMenuLabel>
                
                {Object.entries(formatLabels).map(([format, label]) => {
                    const Icon = formatIcons[format as keyof typeof formatIcons];
                    return (
                        <DropdownMenuItem
                            key={format}
                            onClick={() => handleExport(format as ExportOptions['format'])}
                            className="gap-2"
                        >
                            <Icon className="h-4 w-4" />
                            <span>تصدير {label}</span>
                            {format === 'excel' && (
                                <Badge variant="outline" className="text-xs">
                                    مُوصى
                                </Badge>
                            )}
                        </DropdownMenuItem>
                    );
                })}

                <DropdownMenuSeparator />

                {/* Export Options */}
                <DropdownMenuLabel className="text-xs text-muted-foreground">
                    خيارات التصدير
                </DropdownMenuLabel>

                <DropdownMenuCheckboxItem
                    checked={exportOptions.includeHeaders}
                    onCheckedChange={(checked) =>
                        setExportOptions(prev => ({ ...prev, includeHeaders: checked }))
                    }
                >
                    تضمين رؤوس الأعمدة
                </DropdownMenuCheckboxItem>

                {selectedCount > 0 && (
                    <DropdownMenuCheckboxItem
                        checked={exportOptions.selectedOnly}
                        onCheckedChange={(checked) =>
                            setExportOptions(prev => ({ ...prev, selectedOnly: checked }))
                        }
                    >
                        المحدد فقط ({selectedCount} عنصر)
                    </DropdownMenuCheckboxItem>
                )}

                {/* Column Selection */}
                {availableColumns.length > 0 && (
                    <>
                        <DropdownMenuSeparator />
                        <DropdownMenuSub>
                            <DropdownMenuSubTrigger className="gap-2">
                                <Settings className="h-4 w-4" />
                                اختيار الأعمدة
                                <Badge variant="outline" className="text-xs">
                                    {exportOptions.columns.length}/{availableColumns.length}
                                </Badge>
                            </DropdownMenuSubTrigger>
                            <DropdownMenuSubContent className="w-48">
                                <DropdownMenuLabel className="text-xs">
                                    الأعمدة المراد تصديرها
                                </DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                
                                {/* Select All / None */}
                                <DropdownMenuItem
                                    onClick={() =>
                                        setExportOptions(prev => ({
                                            ...prev,
                                            columns: prev.columns.length === availableColumns.length
                                                ? []
                                                : availableColumns.map(col => col.key)
                                        }))
                                    }
                                    className="gap-2"
                                >
                                    <Check className="h-4 w-4" />
                                    {exportOptions.columns.length === availableColumns.length
                                        ? 'إلغاء تحديد الكل'
                                        : 'تحديد الكل'
                                    }
                                </DropdownMenuItem>
                                
                                <DropdownMenuSeparator />
                                
                                {availableColumns.map((column) => (
                                    <DropdownMenuCheckboxItem
                                        key={column.key}
                                        checked={exportOptions.columns.includes(column.key)}
                                        onCheckedChange={() => toggleColumn(column.key)}
                                    >
                                        {column.label}
                                    </DropdownMenuCheckboxItem>
                                ))}
                            </DropdownMenuSubContent>
                        </DropdownMenuSub>
                    </>
                )}

                <DropdownMenuSeparator />

                {/* Quick Actions */}
                <DropdownMenuItem className="gap-2 text-muted-foreground">
                    <Printer className="h-4 w-4" />
                    طباعة
                </DropdownMenuItem>

                {/* Export Info */}
                <DropdownMenuSeparator />
                <div className="px-2 py-1.5 text-xs text-muted-foreground">
                    {exportOptions.selectedOnly && selectedCount > 0
                        ? `سيتم تصدير ${selectedCount} عنصر محدد`
                        : `سيتم تصدير ${totalCount} عنصر`
                    }
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
