import React from 'react';
import { Calendar, GraduationCap, MapPin, Phone, User, UserCheck, Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    HoverCard,
    HoverCardContent,
    HoverCardTrigger,
} from '@/components/ui/hover-card';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface User {
    id: string;
    name: string;
    phone: string;
    gender: 'male' | 'female';
    birth_date?: string;
    address?: string;
    college: string;
    department: string;
    academic_year: number;
    first_attendance_date?: string;
    is_active: boolean;
    created_at: string;
}

interface UserHoverCardProps {
    user: User;
    children: React.ReactNode;
    side?: 'top' | 'right' | 'bottom' | 'left';
    align?: 'start' | 'center' | 'end';
    className?: string;
    isRTL?: boolean;
}

export function UserHoverCard({
    user,
    children,
    side = 'right',
    align = 'start',
    className,
    isRTL = true,
}: UserHoverCardProps) {
    const formatDate = (dateString?: string) => {
        if (!dateString) return 'غير محدد';
        try {
            return format(new Date(dateString), 'dd MMMM yyyy', { locale: ar });
        } catch {
            return 'تاريخ غير صحيح';
        }
    };

    const getYearName = (year: number) => {
        const yearNames = {
            1: 'السنة الأولى',
            2: 'السنة الثانية',
            3: 'السنة الثالثة',
            4: 'السنة الرابعة',
        };
        return yearNames[year as keyof typeof yearNames] || `السنة ${year}`;
    };

    return (
        <HoverCard openDelay={300} closeDelay={100}>
            <HoverCardTrigger asChild>
                {children}
            </HoverCardTrigger>
            <HoverCardContent 
                side={side} 
                align={align}
                className={cn('w-80 p-0', className)}
                dir={isRTL ? 'rtl' : 'ltr'}
            >
                <Card className="border-0 shadow-none">
                    <CardHeader className="pb-3">
                        <div className="flex items-center gap-3">
                            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500">
                                <span className="text-lg font-bold text-white">
                                    {user.name.charAt(0)}
                                </span>
                            </div>
                            <div className="flex-1 min-w-0">
                                <CardTitle className="text-base font-semibold truncate">
                                    {user.name}
                                </CardTitle>
                                <div className="flex items-center gap-2 mt-1">
                                    <Badge 
                                        variant={user.is_active ? 'default' : 'secondary'}
                                        className="text-xs"
                                    >
                                        {user.is_active ? 'نشط' : 'غير نشط'}
                                    </Badge>
                                    <Badge 
                                        variant={user.gender === 'male' ? 'outline' : 'secondary'}
                                        className="text-xs"
                                    >
                                        {user.gender === 'male' ? 'ذكر' : 'أنثى'}
                                    </Badge>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-3 pt-0">
                        {/* Contact Information */}
                        <div className="space-y-2">
                            <div className="flex items-center gap-2 text-sm">
                                <Phone className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">{user.phone}</span>
                            </div>
                            
                            {user.address && (
                                <div className="flex items-center gap-2 text-sm">
                                    <MapPin className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-muted-foreground truncate">
                                        {user.address}
                                    </span>
                                </div>
                            )}
                        </div>

                        {/* Academic Information */}
                        <div className="space-y-2 border-t pt-3">
                            <div className="flex items-center gap-2 text-sm">
                                <GraduationCap className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1 min-w-0">
                                    <div className="font-medium truncate">{user.college}</div>
                                    <div className="text-xs text-muted-foreground truncate">
                                        {user.department}
                                    </div>
                                </div>
                            </div>
                            
                            <div className="flex items-center gap-2 text-sm">
                                <User className="h-4 w-4 text-muted-foreground" />
                                <span className="text-muted-foreground">
                                    {getYearName(user.academic_year)}
                                </span>
                            </div>
                        </div>

                        {/* Dates Information */}
                        <div className="space-y-2 border-t pt-3">
                            {user.birth_date && (
                                <div className="flex items-center gap-2 text-sm">
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                    <div className="flex-1">
                                        <div className="text-xs text-muted-foreground">تاريخ الميلاد</div>
                                        <div className="text-xs">{formatDate(user.birth_date)}</div>
                                    </div>
                                </div>
                            )}
                            
                            {user.first_attendance_date && (
                                <div className="flex items-center gap-2 text-sm">
                                    <UserCheck className="h-4 w-4 text-green-600" />
                                    <div className="flex-1">
                                        <div className="text-xs text-muted-foreground">أول حضور</div>
                                        <div className="text-xs text-green-600">
                                            {formatDate(user.first_attendance_date)}
                                        </div>
                                    </div>
                                </div>
                            )}
                            
                            <div className="flex items-center gap-2 text-sm">
                                <Clock className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                    <div className="text-xs text-muted-foreground">تاريخ التسجيل</div>
                                    <div className="text-xs">{formatDate(user.created_at)}</div>
                                </div>
                            </div>
                        </div>

                        {/* Quick Actions */}
                        <div className="border-t pt-3">
                            <div className="text-xs text-muted-foreground text-center">
                                انقر للمزيد من التفاصيل
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </HoverCardContent>
        </HoverCard>
    );
}
