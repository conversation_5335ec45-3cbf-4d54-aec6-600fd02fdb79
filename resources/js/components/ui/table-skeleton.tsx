import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface TableSkeletonProps {
    rows?: number;
    columns?: number;
    showHeader?: boolean;
    className?: string;
}

export function TableSkeleton({ 
    rows = 10, 
    columns = 6, 
    showHeader = true,
    className 
}: TableSkeletonProps) {
    return (
        <div className={className}>
            <Table>
                {showHeader && (
                    <TableHeader>
                        <TableRow>
                            {Array.from({ length: columns }).map((_, index) => (
                                <TableHead key={index}>
                                    <Skeleton className="h-4 w-20" />
                                </TableHead>
                            ))}
                        </TableRow>
                    </TableHeader>
                )}
                <TableBody>
                    {Array.from({ length: rows }).map((_, rowIndex) => (
                        <TableRow key={rowIndex}>
                            {Array.from({ length: columns }).map((_, colIndex) => (
                                <TableCell key={colIndex}>
                                    {colIndex === 0 ? (
                                        // First column with avatar and name
                                        <div className="flex items-center gap-3">
                                            <Skeleton className="h-10 w-10 rounded-full" />
                                            <div className="space-y-1">
                                                <Skeleton className="h-4 w-24" />
                                                <Skeleton className="h-3 w-20" />
                                            </div>
                                        </div>
                                    ) : colIndex === 1 ? (
                                        // Second column with badge
                                        <Skeleton className="h-6 w-16 rounded-full" />
                                    ) : colIndex === columns - 1 ? (
                                        // Last column with actions
                                        <div className="flex items-center gap-2">
                                            <Skeleton className="h-8 w-8 rounded" />
                                            <Skeleton className="h-8 w-8 rounded" />
                                            <Skeleton className="h-8 w-8 rounded" />
                                        </div>
                                    ) : (
                                        // Regular content
                                        <Skeleton className="h-4 w-16" />
                                    )}
                                </TableCell>
                            ))}
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    );
}

interface UserTableSkeletonProps {
    rows?: number;
    className?: string;
}

export function UserTableSkeleton({ rows = 10, className }: UserTableSkeletonProps) {
    return (
        <div className={className}>
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-12">
                            <Skeleton className="h-4 w-4" />
                        </TableHead>
                        <TableHead>
                            <Skeleton className="h-4 w-16" />
                        </TableHead>
                        <TableHead>
                            <Skeleton className="h-4 w-20" />
                        </TableHead>
                        <TableHead>
                            <Skeleton className="h-4 w-16" />
                        </TableHead>
                        <TableHead>
                            <Skeleton className="h-4 w-20" />
                        </TableHead>
                        <TableHead>
                            <Skeleton className="h-4 w-16" />
                        </TableHead>
                        <TableHead>
                            <Skeleton className="h-4 w-16" />
                        </TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {Array.from({ length: rows }).map((_, index) => (
                        <TableRow key={index}>
                            {/* Checkbox */}
                            <TableCell>
                                <Skeleton className="h-4 w-4" />
                            </TableCell>
                            
                            {/* Name with avatar */}
                            <TableCell>
                                <div className="flex items-center gap-3">
                                    <Skeleton className="h-10 w-10 rounded-full" />
                                    <div className="space-y-1">
                                        <Skeleton className="h-4 w-32" />
                                        <Skeleton className="h-3 w-24" />
                                    </div>
                                </div>
                            </TableCell>
                            
                            {/* Academic Year Badge */}
                            <TableCell>
                                <Skeleton className="h-6 w-16 rounded-full" />
                            </TableCell>
                            
                            {/* Gender */}
                            <TableCell>
                                <Skeleton className="h-4 w-12" />
                            </TableCell>
                            
                            {/* Birth Date */}
                            <TableCell>
                                <div className="flex items-center gap-2">
                                    <Skeleton className="h-4 w-4" />
                                    <Skeleton className="h-4 w-20" />
                                </div>
                            </TableCell>
                            
                            {/* First Attendance */}
                            <TableCell>
                                <Skeleton className="h-4 w-20" />
                            </TableCell>
                            
                            {/* Actions */}
                            <TableCell>
                                <div className="flex items-center gap-2">
                                    <Skeleton className="h-8 w-8 rounded" />
                                    <Skeleton className="h-8 w-8 rounded" />
                                    <Skeleton className="h-8 w-8 rounded" />
                                </div>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    );
}

interface SearchSkeletonProps {
    className?: string;
}

export function SearchSkeleton({ className }: SearchSkeletonProps) {
    return (
        <div className={className}>
            <div className="space-y-4">
                {/* Search input */}
                <Skeleton className="h-11 w-full" />
                
                {/* Filter controls */}
                <div className="flex items-center gap-2">
                    <Skeleton className="h-9 w-32" />
                    <div className="flex gap-1">
                        <Skeleton className="h-6 w-20 rounded-full" />
                        <Skeleton className="h-6 w-16 rounded-full" />
                        <Skeleton className="h-6 w-24 rounded-full" />
                    </div>
                </div>
            </div>
        </div>
    );
}

interface StatsSkeleton {
    className?: string;
}

export function StatsSkeleton({ className }: StatsSkeleton) {
    return (
        <div className={className}>
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {Array.from({ length: 4 }).map((_, index) => (
                    <div key={index} className="rounded-lg border p-4">
                        <div className="flex items-center gap-3">
                            <Skeleton className="h-10 w-10 rounded-lg" />
                            <div className="space-y-1">
                                <Skeleton className="h-4 w-16" />
                                <Skeleton className="h-6 w-12" />
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}
