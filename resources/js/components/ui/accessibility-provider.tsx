import React, { createContext, useContext, useEffect, useState } from 'react';

interface AccessibilityContextType {
    highContrast: boolean;
    reducedMotion: boolean;
    fontSize: 'small' | 'medium' | 'large';
    screenReader: boolean;
    keyboardNavigation: boolean;
    toggleHighContrast: () => void;
    toggleReducedMotion: () => void;
    setFontSize: (size: 'small' | 'medium' | 'large') => void;
    announceToScreenReader: (message: string) => void;
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

export function useAccessibility() {
    const context = useContext(AccessibilityContext);
    if (!context) {
        throw new Error('useAccessibility must be used within an AccessibilityProvider');
    }
    return context;
}

interface AccessibilityProviderProps {
    children: React.ReactNode;
}

export function AccessibilityProvider({ children }: AccessibilityProviderProps) {
    const [highContrast, setHighContrast] = useState(false);
    const [reducedMotion, setReducedMotion] = useState(false);
    const [fontSize, setFontSizeState] = useState<'small' | 'medium' | 'large'>('medium');
    const [screenReader, setScreenReader] = useState(false);
    const [keyboardNavigation, setKeyboardNavigation] = useState(false);

    // Detect user preferences
    useEffect(() => {
        // Check for reduced motion preference
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        setReducedMotion(prefersReducedMotion);

        // Check for high contrast preference
        const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
        setHighContrast(prefersHighContrast);

        // Detect screen reader usage
        const hasScreenReader = window.navigator.userAgent.includes('NVDA') || 
                               window.navigator.userAgent.includes('JAWS') || 
                               window.speechSynthesis !== undefined;
        setScreenReader(hasScreenReader);

        // Load saved preferences
        const savedPrefs = localStorage.getItem('accessibility-preferences');
        if (savedPrefs) {
            try {
                const prefs = JSON.parse(savedPrefs);
                setHighContrast(prefs.highContrast ?? prefersHighContrast);
                setReducedMotion(prefs.reducedMotion ?? prefersReducedMotion);
                setFontSizeState(prefs.fontSize ?? 'medium');
            } catch (error) {
                console.warn('Failed to load accessibility preferences:', error);
            }
        }
    }, []);

    // Detect keyboard navigation
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Tab') {
                setKeyboardNavigation(true);
            }
        };

        const handleMouseDown = () => {
            setKeyboardNavigation(false);
        };

        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('mousedown', handleMouseDown);

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.removeEventListener('mousedown', handleMouseDown);
        };
    }, []);

    // Apply accessibility classes to document
    useEffect(() => {
        const root = document.documentElement;
        
        // High contrast
        if (highContrast) {
            root.classList.add('high-contrast');
        } else {
            root.classList.remove('high-contrast');
        }

        // Reduced motion
        if (reducedMotion) {
            root.classList.add('reduced-motion');
        } else {
            root.classList.remove('reduced-motion');
        }

        // Font size
        root.classList.remove('font-small', 'font-medium', 'font-large');
        root.classList.add(`font-${fontSize}`);

        // Keyboard navigation
        if (keyboardNavigation) {
            root.classList.add('keyboard-navigation');
        } else {
            root.classList.remove('keyboard-navigation');
        }
    }, [highContrast, reducedMotion, fontSize, keyboardNavigation]);

    // Save preferences
    useEffect(() => {
        const preferences = {
            highContrast,
            reducedMotion,
            fontSize,
        };
        localStorage.setItem('accessibility-preferences', JSON.stringify(preferences));
    }, [highContrast, reducedMotion, fontSize]);

    const toggleHighContrast = () => {
        setHighContrast(!highContrast);
    };

    const toggleReducedMotion = () => {
        setReducedMotion(!reducedMotion);
    };

    const setFontSize = (size: 'small' | 'medium' | 'large') => {
        setFontSizeState(size);
    };

    const announceToScreenReader = (message: string) => {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = message;
        
        document.body.appendChild(announcement);
        
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    };

    const value: AccessibilityContextType = {
        highContrast,
        reducedMotion,
        fontSize,
        screenReader,
        keyboardNavigation,
        toggleHighContrast,
        toggleReducedMotion,
        setFontSize,
        announceToScreenReader,
    };

    return (
        <AccessibilityContext.Provider value={value}>
            {children}
        </AccessibilityContext.Provider>
    );
}
