import * as React from 'react';
import {
    ColumnDef,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getSortedRowModel,
    useReactTable,
    PaginationState,
} from '@tanstack/react-table';
import { ArrowUpDown, ChevronDown, MoreHorizontal, Download, RefreshCw } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuTrigger,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { AdvancedSearch, SearchFilters, FilterOptions } from './advanced-search';
import { UserTableSkeleton } from './table-skeleton';

interface PaginationInfo {
    pageIndex: number;
    pageSize: number;
    pageCount: number;
    total: number;
}

interface EnhancedDataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    
    // Search and filtering
    filters: SearchFilters;
    onFiltersChange: (filters: SearchFilters) => void;
    filterOptions?: FilterOptions;
    
    // Pagination
    pagination?: PaginationInfo;
    onPaginationChange?: (updater: ((old: PaginationState) => PaginationState) | PaginationState) => void;
    
    // Sorting
    sorting?: SortingState;
    onSortingChange?: (updater: ((old: SortingState) => SortingState) | SortingState) => void;
    
    // Loading states
    isLoading?: boolean;
    isFetching?: boolean;
    isError?: boolean;
    error?: Error | null;
    
    // Actions
    onRefresh?: () => void;
    onExport?: () => void;
    
    // Bulk actions
    selectedRows?: string[];
    onRowSelectionChange?: (selectedIds: string[]) => void;
    bulkActions?: React.ReactNode;
    
    // UI customization
    className?: string;
    isRTL?: boolean;
    enableColumnVisibility?: boolean;
    
    // Summary info
    summary?: {
        total_users: number;
        active_users: number;
        filtered_count: number;
    };
}

export function EnhancedDataTable<TData, TValue>({
    columns,
    data,
    filters,
    onFiltersChange,
    filterOptions,
    pagination,
    onPaginationChange,
    sorting,
    onSortingChange,
    isLoading = false,
    isFetching = false,
    isError = false,
    error,
    onRefresh,
    onExport,
    selectedRows = [],
    onRowSelectionChange,
    bulkActions,
    className,
    isRTL = true,
    enableColumnVisibility = true,
    summary,
}: EnhancedDataTableProps<TData, TValue>) {
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState({});

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        onSortingChange,
        onPaginationChange,
        manualPagination: true,
        manualSorting: true,
        pageCount: pagination?.pageCount ?? 0,
        state: {
            sorting,
            columnVisibility,
            rowSelection,
            pagination: pagination ? {
                pageIndex: pagination.pageIndex,
                pageSize: pagination.pageSize,
            } : undefined,
        },
    });

    // Handle row selection
    React.useEffect(() => {
        const selectedIds = table.getSelectedRowModel().rows.map(row => 
            (row.original as any).id
        );
        if (onRowSelectionChange && selectedIds.length !== selectedRows.length) {
            onRowSelectionChange(selectedIds);
        }
    }, [rowSelection, onRowSelectionChange, selectedRows.length, table]);

    if (isError) {
        return (
            <div className="flex flex-col items-center justify-center py-12">
                <div className="text-center">
                    <h3 className="text-lg font-semibold text-destructive mb-2">
                        حدث خطأ في تحميل البيانات
                    </h3>
                    <p className="text-muted-foreground mb-4">
                        {error?.message || 'خطأ غير معروف'}
                    </p>
                    {onRefresh && (
                        <Button onClick={onRefresh} variant="outline">
                            <RefreshCw className="h-4 w-4 mr-2" />
                            إعادة المحاولة
                        </Button>
                    )}
                </div>
            </div>
        );
    }

    return (
        <div className={cn('space-y-4', className)}>
            {/* Search and Filters */}
            <AdvancedSearch
                filters={filters}
                onFiltersChange={onFiltersChange}
                filterOptions={filterOptions}
                isSearching={isFetching}
                isRTL={isRTL}
            />

            {/* Toolbar */}
            <div className={cn(
                'flex items-center justify-between',
                isRTL ? 'flex-row-reverse' : 'flex-row'
            )}>
                <div className={cn(
                    'flex items-center gap-2',
                    isRTL ? 'flex-row-reverse' : 'flex-row'
                )}>
                    {/* Summary Info */}
                    {summary && (
                        <div className={cn(
                            'flex items-center gap-4 text-sm text-muted-foreground',
                            isRTL ? 'flex-row-reverse' : 'flex-row'
                        )}>
                            <span>المجموع: {summary.total_users}</span>
                            <span>النشط: {summary.active_users}</span>
                            <span>المفلتر: {summary.filtered_count}</span>
                        </div>
                    )}

                    {/* Bulk Actions */}
                    {selectedRows.length > 0 && bulkActions && (
                        <div className={cn(
                            'flex items-center gap-2',
                            isRTL ? 'flex-row-reverse' : 'flex-row'
                        )}>
                            <Badge variant="secondary">
                                {selectedRows.length} محدد
                            </Badge>
                            {bulkActions}
                        </div>
                    )}
                </div>

                <div className={cn(
                    'flex items-center gap-2',
                    isRTL ? 'flex-row-reverse' : 'flex-row'
                )}>
                    {/* Refresh Button */}
                    {onRefresh && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={onRefresh}
                            disabled={isFetching}
                        >
                            <RefreshCw className={cn('h-4 w-4', isFetching && 'animate-spin')} />
                        </Button>
                    )}

                    {/* Export Button */}
                    {onExport && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={onExport}
                        >
                            <Download className="h-4 w-4" />
                            تصدير
                        </Button>
                    )}

                    {/* Column Visibility */}
                    {enableColumnVisibility && (
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm">
                                    الأعمدة
                                    <ChevronDown className="h-4 w-4" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align={isRTL ? 'end' : 'start'}>
                                {table
                                    .getAllColumns()
                                    .filter((column) => column.getCanHide())
                                    .map((column) => {
                                        return (
                                            <DropdownMenuCheckboxItem
                                                key={column.id}
                                                className="capitalize"
                                                checked={column.getIsVisible()}
                                                onCheckedChange={(value) =>
                                                    column.toggleVisibility(!!value)
                                                }
                                            >
                                                {column.id}
                                            </DropdownMenuCheckboxItem>
                                        );
                                    })}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    )}
                </div>
            </div>

            {/* Table */}
            <div className="rounded-md border">
                {isLoading ? (
                    <UserTableSkeleton rows={pagination?.pageSize || 10} />
                ) : (
                    <Table>
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id} className="text-right">
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                      header.column.columnDef.header,
                                                      header.getContext()
                                                  )}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow
                                        key={row.id}
                                        data-state={row.getIsSelected() && 'selected'}
                                        className="hover:bg-muted/50"
                                    >
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id} className="text-right">
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell
                                        colSpan={columns.length}
                                        className="h-24 text-center"
                                    >
                                        لا توجد نتائج.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                )}
            </div>

            {/* Pagination */}
            {pagination && (
                <div className={cn(
                    'flex items-center justify-between',
                    isRTL ? 'flex-row-reverse' : 'flex-row'
                )}>
                    <div className="text-sm text-muted-foreground">
                        عرض {pagination.pageIndex * pagination.pageSize + 1} إلى{' '}
                        {Math.min((pagination.pageIndex + 1) * pagination.pageSize, pagination.total)} من{' '}
                        {pagination.total} نتيجة
                    </div>
                    <div className={cn(
                        'flex items-center gap-2',
                        isRTL ? 'flex-row-reverse' : 'flex-row'
                    )}>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => table.previousPage()}
                            disabled={!table.getCanPreviousPage()}
                        >
                            السابق
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => table.nextPage()}
                            disabled={!table.getCanNextPage()}
                        >
                            التالي
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
}
