import React, { useEffect, useState } from 'react';
import { Keyboard, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface KeyboardShortcut {
    key: string;
    description: string;
    category: string;
    action?: () => void;
}

interface KeyboardShortcutsProps {
    shortcuts: KeyboardShortcut[];
    onShortcut?: (key: string) => void;
    className?: string;
    isRTL?: boolean;
}

const defaultShortcuts: KeyboardShortcut[] = [
    { key: 'Ctrl+K', description: 'فتح البحث السريع', category: 'البحث' },
    { key: 'Ctrl+N', description: 'إضافة طالب جديد', category: 'الإجراءات' },
    { key: 'Ctrl+E', description: 'تصدير البيانات', category: 'الإجراءات' },
    { key: 'Ctrl+R', description: 'تحديث البيانات', category: 'الإجراءات' },
    { key: 'Ctrl+A', description: 'تحديد الكل', category: 'التحديد' },
    { key: 'Ctrl+D', description: 'إلغاء التحديد', category: 'التحديد' },
    { key: 'Delete', description: 'حذف المحدد', category: 'الإجراءات' },
    { key: 'Enter', description: 'عرض التفاصيل', category: 'التنقل' },
    { key: 'Escape', description: 'إغلاق النوافذ', category: 'التنقل' },
    { key: '?', description: 'عرض الاختصارات', category: 'المساعدة' },
];

export function KeyboardShortcuts({
    shortcuts = defaultShortcuts,
    onShortcut,
    className,
    isRTL = true,
}: KeyboardShortcutsProps) {
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            // Don't trigger shortcuts when typing in inputs
            if (
                event.target instanceof HTMLInputElement ||
                event.target instanceof HTMLTextAreaElement ||
                event.target instanceof HTMLSelectElement
            ) {
                return;
            }

            const key = getKeyString(event);
            
            // Handle built-in shortcuts
            switch (key) {
                case '?':
                    if (!event.ctrlKey && !event.metaKey) {
                        event.preventDefault();
                        setIsOpen(true);
                    }
                    break;
                case 'Escape':
                    event.preventDefault();
                    setIsOpen(false);
                    break;
                default:
                    // Call custom shortcut handler
                    if (onShortcut) {
                        const shortcut = shortcuts.find(s => s.key === key);
                        if (shortcut) {
                            event.preventDefault();
                            onShortcut(key);
                            if (shortcut.action) {
                                shortcut.action();
                            }
                        }
                    }
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [shortcuts, onShortcut]);

    const getKeyString = (event: KeyboardEvent): string => {
        const parts: string[] = [];
        
        if (event.ctrlKey || event.metaKey) parts.push('Ctrl');
        if (event.altKey) parts.push('Alt');
        if (event.shiftKey) parts.push('Shift');
        
        if (event.key === ' ') {
            parts.push('Space');
        } else if (event.key.length === 1) {
            parts.push(event.key.toUpperCase());
        } else {
            parts.push(event.key);
        }
        
        return parts.join('+');
    };

    const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
        if (!acc[shortcut.category]) {
            acc[shortcut.category] = [];
        }
        acc[shortcut.category].push(shortcut);
        return acc;
    }, {} as Record<string, KeyboardShortcut[]>);

    const renderKey = (key: string) => {
        const parts = key.split('+');
        return (
            <div className={cn(
                'flex items-center gap-1',
                isRTL && 'flex-row-reverse'
            )}>
                {parts.map((part, index) => (
                    <React.Fragment key={part}>
                        <Badge variant="outline" className="px-2 py-1 text-xs font-mono">
                            {part === 'Ctrl' ? (navigator.platform.includes('Mac') ? '⌘' : 'Ctrl') : part}
                        </Badge>
                        {index < parts.length - 1 && <span className="text-muted-foreground">+</span>}
                    </React.Fragment>
                ))}
            </div>
        );
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button
                    variant="ghost"
                    size="sm"
                    className={cn('gap-2', className)}
                    title="اختصارات لوحة المفاتيح"
                >
                    <Keyboard className="h-4 w-4" />
                    <span className="hidden sm:inline">اختصارات</span>
                </Button>
            </DialogTrigger>
            
            <DialogContent className="max-w-2xl">
                <DialogHeader>
                    <DialogTitle className={cn(
                        'flex items-center gap-2',
                        isRTL && 'flex-row-reverse'
                    )}>
                        <Keyboard className="h-5 w-5" />
                        اختصارات لوحة المفاتيح
                    </DialogTitle>
                </DialogHeader>
                
                <div className="space-y-6">
                    {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
                        <div key={category} className="space-y-3">
                            <h3 className="font-semibold text-lg">{category}</h3>
                            <div className="space-y-2">
                                {categoryShortcuts.map((shortcut) => (
                                    <div
                                        key={shortcut.key}
                                        className={cn(
                                            'flex items-center justify-between rounded-lg border p-3',
                                            isRTL && 'flex-row-reverse'
                                        )}
                                    >
                                        <span className="text-sm">{shortcut.description}</span>
                                        {renderKey(shortcut.key)}
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
                
                <div className="mt-6 rounded-lg bg-muted p-4">
                    <p className="text-sm text-muted-foreground">
                        💡 نصيحة: اضغط على <Badge variant="outline" className="mx-1">؟</Badge> في أي وقت لعرض هذه النافذة
                    </p>
                </div>
            </DialogContent>
        </Dialog>
    );
}

// Hook for using keyboard shortcuts in components
export function useKeyboardShortcuts(shortcuts: Record<string, () => void>) {
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            // Don't trigger shortcuts when typing in inputs
            if (
                event.target instanceof HTMLInputElement ||
                event.target instanceof HTMLTextAreaElement ||
                event.target instanceof HTMLSelectElement
            ) {
                return;
            }

            const key = getKeyString(event);
            const handler = shortcuts[key];
            
            if (handler) {
                event.preventDefault();
                handler();
            }
        };

        const getKeyString = (event: KeyboardEvent): string => {
            const parts: string[] = [];
            
            if (event.ctrlKey || event.metaKey) parts.push('Ctrl');
            if (event.altKey) parts.push('Alt');
            if (event.shiftKey) parts.push('Shift');
            
            if (event.key === ' ') {
                parts.push('Space');
            } else if (event.key.length === 1) {
                parts.push(event.key.toUpperCase());
            } else {
                parts.push(event.key);
            }
            
            return parts.join('+');
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [shortcuts]);
}
