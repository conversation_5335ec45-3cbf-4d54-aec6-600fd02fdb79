import React, { useRef, useEffect, useState } from 'react';
import { Search, X, Clock, Zap, TrendingUp, History } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface SearchSuggestion {
    text: string;
    type: 'recent' | 'suggestion' | 'popular';
    count?: number;
}

interface EnhancedSearchInputProps {
    value: string;
    onChange: (value: string) => void;
    onClear: () => void;
    placeholder?: string;
    isSearching?: boolean;
    searchResults?: any[];
    className?: string;
    isRTL?: boolean;
    disabled?: boolean;
    showSearchStats?: boolean;
    cacheHit?: boolean;
    totalResults?: number;
    searchTime?: number;
    suggestions?: SearchSuggestion[];
    onSuggestionSelect?: (suggestion: string) => void;
    showSuggestions?: boolean;
    recentSearches?: string[];
}

export function EnhancedSearchInput({
    value,
    onChange,
    onClear,
    placeholder = "البحث في الطلاب...",
    isSearching = false,
    searchResults = [],
    className,
    isRTL = true,
    disabled = false,
    showSearchStats = true,
    cacheHit = false,
    totalResults,
    searchTime,
    suggestions = [],
    onSuggestionSelect,
    showSuggestions = true,
    recentSearches = [],
}: EnhancedSearchInputProps) {
    const inputRef = useRef<HTMLInputElement>(null);
    const [showSuggestionsDropdown, setShowSuggestionsDropdown] = useState(false);
    const [focusedSuggestionIndex, setFocusedSuggestionIndex] = useState(-1);

    // Enhanced keyboard handling
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                inputRef.current?.focus();
                setShowSuggestionsDropdown(true);
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, []);

    // Handle input focus and blur
    const handleInputFocus = () => {
        if (showSuggestions && (suggestions.length > 0 || recentSearches.length > 0)) {
            setShowSuggestionsDropdown(true);
        }
    };

    const handleInputBlur = () => {
        // Delay hiding to allow suggestion clicks
        setTimeout(() => {
            setShowSuggestionsDropdown(false);
            setFocusedSuggestionIndex(-1);
        }, 150);
    };

    // Handle keyboard navigation in suggestions
    const handleInputKeyDown = (e: React.KeyboardEvent) => {
        if (!showSuggestionsDropdown) return;

        const allSuggestions = [...recentSearches.map(s => ({ text: s, type: 'recent' as const })), ...suggestions];

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setFocusedSuggestionIndex(prev =>
                    prev < allSuggestions.length - 1 ? prev + 1 : 0
                );
                break;
            case 'ArrowUp':
                e.preventDefault();
                setFocusedSuggestionIndex(prev =>
                    prev > 0 ? prev - 1 : allSuggestions.length - 1
                );
                break;
            case 'Enter':
                if (focusedSuggestionIndex >= 0) {
                    e.preventDefault();
                    const selectedSuggestion = allSuggestions[focusedSuggestionIndex];
                    handleSuggestionClick(selectedSuggestion.text);
                }
                break;
            case 'Escape':
                setShowSuggestionsDropdown(false);
                setFocusedSuggestionIndex(-1);
                break;
        }
    };

    // Handle suggestion selection
    const handleSuggestionClick = (suggestionText: string) => {
        onChange(suggestionText);
        onSuggestionSelect?.(suggestionText);
        setShowSuggestionsDropdown(false);
        setFocusedSuggestionIndex(-1);
    };

    const hasValue = value.length > 0;
    const hasResults = searchResults.length > 0;
    const allSuggestions = [...recentSearches.map(s => ({ text: s, type: 'recent' as const })), ...suggestions];
    const shouldShowSuggestions = showSuggestionsDropdown && showSuggestions && allSuggestions.length > 0;

    return (
        <div className={cn('space-y-2', className)}>
            {/* Search Input */}
            <div className="relative group">
                {/* Search Icon */}
                <div className={cn(
                    'absolute top-1/2 -translate-y-1/2 transition-colors',
                    isRTL ? 'right-3' : 'left-3',
                    isSearching ? 'text-primary' : 'text-muted-foreground group-focus-within:text-primary'
                )}>
                    {isSearching ? (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                    ) : (
                        <Search className="h-4 w-4" />
                    )}
                </div>

                {/* Input Field */}
                <input
                    ref={inputRef}
                    type="text"
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    onKeyDown={handleInputKeyDown}
                    placeholder={placeholder}
                    disabled={disabled}
                    className={cn(
                        'h-12 w-full rounded-lg border border-input bg-background px-4 py-3 text-sm transition-all',
                        'placeholder:text-muted-foreground',
                        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                        'hover:border-primary/50 focus-visible:border-primary',
                        'disabled:cursor-not-allowed disabled:opacity-50',
                        isRTL ? 'pr-10 pl-12 text-right' : 'pl-10 pr-12 text-left',
                        hasValue && 'border-primary/50',
                        isSearching && 'border-primary',
                        shouldShowSuggestions && 'rounded-b-none'
                    )}
                    dir={isRTL ? 'rtl' : 'ltr'}
                    autoComplete="off"
                />

                {/* Clear Button */}
                {hasValue && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={onClear}
                        className={cn(
                            'absolute top-1/2 h-6 w-6 -translate-y-1/2 p-0 opacity-0 transition-opacity group-hover:opacity-100',
                            isRTL ? 'left-2' : 'right-2',
                            hasValue && 'opacity-70 hover:opacity-100'
                        )}
                        tabIndex={-1}
                    >
                        <X className="h-3 w-3" />
                    </Button>
                )}

                {/* Keyboard Shortcut Hint */}
                {!hasValue && !isSearching && (
                    <div className={cn(
                        'absolute top-1/2 -translate-y-1/2 opacity-0 transition-opacity group-hover:opacity-100',
                        isRTL ? 'left-3' : 'right-3'
                    )}>
                        <Badge variant="outline" className="text-xs">
                            Ctrl+K
                        </Badge>
                    </div>
                )}

                {/* Suggestions Dropdown */}
                {shouldShowSuggestions && (
                    <div className={cn(
                        'absolute top-full left-0 right-0 z-50 mt-1 max-h-64 overflow-y-auto rounded-lg border border-input bg-background shadow-lg',
                        'animate-in fade-in-0 zoom-in-95'
                    )}>
                        {recentSearches.length > 0 && (
                            <div className="border-b border-border p-2">
                                <div className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-muted-foreground">
                                    <History className="h-3 w-3" />
                                    <span>عمليات البحث الأخيرة</span>
                                </div>
                                {recentSearches.map((search, index) => (
                                    <button
                                        key={`recent-${index}`}
                                        onClick={() => handleSuggestionClick(search)}
                                        className={cn(
                                            'w-full px-3 py-2 text-sm text-left hover:bg-muted rounded-md transition-colors',
                                            isRTL && 'text-right',
                                            index === focusedSuggestionIndex && 'bg-muted'
                                        )}
                                    >
                                        <div className="flex items-center gap-2">
                                            <Clock className="h-3 w-3 text-muted-foreground" />
                                            <span>{search}</span>
                                        </div>
                                    </button>
                                ))}
                            </div>
                        )}

                        {suggestions.length > 0 && (
                            <div className="p-2">
                                <div className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-muted-foreground">
                                    <TrendingUp className="h-3 w-3" />
                                    <span>اقتراحات البحث</span>
                                </div>
                                {suggestions.map((suggestion, index) => {
                                    const adjustedIndex = index + recentSearches.length;
                                    return (
                                        <button
                                            key={`suggestion-${index}`}
                                            onClick={() => handleSuggestionClick(suggestion.text)}
                                            className={cn(
                                                'w-full px-3 py-2 text-sm text-left hover:bg-muted rounded-md transition-colors',
                                                isRTL && 'text-right',
                                                adjustedIndex === focusedSuggestionIndex && 'bg-muted'
                                            )}
                                        >
                                            <div className="flex items-center justify-between">
                                                <span>{suggestion.text}</span>
                                                {suggestion.count && (
                                                    <Badge variant="secondary" className="text-xs">
                                                        {suggestion.count}
                                                    </Badge>
                                                )}
                                            </div>
                                        </button>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                )}
            </div>

            {/* Search Stats */}
            {showSearchStats && hasValue && (
                <div className={cn(
                    'flex items-center gap-2 text-xs text-muted-foreground',
                    isRTL && 'flex-row-reverse'
                )}>
                    {isSearching ? (
                        <div className="flex items-center gap-1">
                            <div className="h-3 w-3 animate-pulse rounded-full bg-primary/50" />
                            <span>جاري البحث...</span>
                        </div>
                    ) : hasResults ? (
                        <div className={cn(
                            'flex items-center gap-3',
                            isRTL && 'flex-row-reverse'
                        )}>
                            {/* Results Count */}
                            <div className="flex items-center gap-1">
                                <Search className="h-3 w-3" />
                                <span>
                                    {totalResults ? 
                                        `${searchResults.length} من ${totalResults} نتيجة` : 
                                        `${searchResults.length} نتيجة`
                                    }
                                </span>
                            </div>

                            {/* Cache Hit Indicator */}
                            {cacheHit && (
                                <div className="flex items-center gap-1 text-green-600">
                                    <Zap className="h-3 w-3" />
                                    <span>نتائج محفوظة</span>
                                </div>
                            )}

                            {/* Search Time */}
                            {searchTime && !cacheHit && (
                                <div className="flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    <span>{searchTime}ms</span>
                                </div>
                            )}
                        </div>
                    ) : hasValue && !isSearching ? (
                        <div className="flex items-center gap-1 text-amber-600">
                            <Search className="h-3 w-3" />
                            <span>لا توجد نتائج</span>
                        </div>
                    ) : null}
                </div>
            )}

            {/* Search Suggestions/Recent Searches */}
            {hasValue && !isSearching && searchResults.length === 0 && (
                <div className="rounded-lg border border-dashed border-muted-foreground/25 p-3">
                    <p className="text-center text-sm text-muted-foreground">
                        لم يتم العثور على نتائج لـ "{value}"
                    </p>
                    <p className="text-center text-xs text-muted-foreground mt-1">
                        جرب البحث بكلمات مختلفة أو تحقق من الإملاء
                    </p>
                </div>
            )}
        </div>
    );
}
