import React from 'react';
import { cn } from '@/lib/utils';

interface SearchHighlightProps {
    text: string;
    className?: string;
    highlightClassName?: string;
}

/**
 * Component to display text with search highlights
 * Expects text with <mark> tags for highlighting
 */
export function SearchHighlight({ 
    text, 
    className,
    highlightClassName = "bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100 px-0.5 rounded-sm font-medium"
}: SearchHighlightProps) {
    // If no highlight tags, return plain text
    if (!text.includes('<mark')) {
        return <span className={className}>{text}</span>;
    }

    // Parse the text and create highlighted elements
    const parts = text.split(/(<mark[^>]*>.*?<\/mark>)/g);
    
    return (
        <span className={className}>
            {parts.map((part, index) => {
                if (part.startsWith('<mark')) {
                    // Extract content from mark tag
                    const content = part.replace(/<mark[^>]*>(.*?)<\/mark>/g, '$1');
                    return (
                        <mark 
                            key={index} 
                            className={cn(
                                "search-highlight",
                                highlightClassName
                            )}
                        >
                            {content}
                        </mark>
                    );
                }
                return part;
            })}
        </span>
    );
}

interface SearchResultHighlightProps {
    user: any; // User with highlighted fields
    searchQuery: string;
    className?: string;
}

/**
 * Component to display user search results with field-specific highlighting
 */
export function SearchResultHighlight({ 
    user, 
    searchQuery,
    className 
}: SearchResultHighlightProps) {
    const hasHighlights = user.highlighted && Object.keys(user.highlighted).length > 0;
    
    if (!hasHighlights) {
        return (
            <div className={cn("space-y-1", className)}>
                <div className="font-medium">{user.name}</div>
                <div className="text-sm text-muted-foreground">{user.phone}</div>
                {user.college && (
                    <div className="text-xs text-muted-foreground">{user.college}</div>
                )}
            </div>
        );
    }

    return (
        <div className={cn("space-y-1", className)}>
            {/* Name with highlighting */}
            <div className="font-medium">
                <SearchHighlight 
                    text={user.highlighted.name || user.name}
                    highlightClassName="bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100 px-1 rounded font-bold"
                />
            </div>
            
            {/* Phone with highlighting */}
            <div className="text-sm text-muted-foreground">
                <SearchHighlight 
                    text={user.highlighted.phone || user.phone}
                    highlightClassName="bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-100 px-1 rounded font-medium"
                />
            </div>
            
            {/* College with highlighting */}
            {(user.highlighted.college || user.college) && (
                <div className="text-xs text-muted-foreground">
                    <SearchHighlight 
                        text={user.highlighted.college || user.college}
                        highlightClassName="bg-purple-100 dark:bg-purple-900 text-purple-900 dark:text-purple-100 px-1 rounded"
                    />
                </div>
            )}
            
            {/* Department with highlighting */}
            {(user.highlighted.department || user.department) && (
                <div className="text-xs text-muted-foreground">
                    <SearchHighlight 
                        text={user.highlighted.department || user.department}
                        highlightClassName="bg-orange-100 dark:bg-orange-900 text-orange-900 dark:text-orange-100 px-1 rounded"
                    />
                </div>
            )}
        </div>
    );
}

interface SearchMatchBadgeProps {
    matches?: any[];
    className?: string;
}

/**
 * Badge showing search match information
 */
export function SearchMatchBadge({ matches = [], className }: SearchMatchBadgeProps) {
    if (!matches.length) return null;
    
    const fieldCounts = matches.reduce((acc, match) => {
        const field = match.key;
        acc[field] = (acc[field] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);
    
    const fieldNames = {
        name: 'الاسم',
        phone: 'الهاتف',
        college: 'الكلية',
        department: 'القسم'
    };
    
    return (
        <div className={cn("flex flex-wrap gap-1", className)}>
            {Object.entries(fieldCounts).map(([field, count]) => (
                <span 
                    key={field}
                    className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                    {fieldNames[field as keyof typeof fieldNames] || field}
                    {count > 1 && ` (${count})`}
                </span>
            ))}
        </div>
    );
}
