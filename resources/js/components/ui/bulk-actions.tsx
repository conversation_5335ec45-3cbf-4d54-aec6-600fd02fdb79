import React from 'react';
import {
    User<PERSON>heck,
    UserX,
    Trash2,
    Edit,
    Download,
    Mail,
    MessageSquare,
    Archive,
    MoreHorizontal,
    X,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

export interface BulkAction {
    id: string;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
    variant?: 'default' | 'destructive' | 'outline' | 'secondary';
    requiresConfirmation?: boolean;
    confirmationMessage?: string;
}

interface BulkActionsProps {
    selectedCount: number;
    totalCount: number;
    onClearSelection: () => void;
    onAction: (actionId: string) => void;
    actions?: BulkAction[];
    className?: string;
    isRTL?: boolean;
    compact?: boolean;
}

const defaultActions: BulkAction[] = [
    {
        id: 'mark_present',
        label: 'تسجيل حضور',
        icon: UserCheck,
        variant: 'default',
    },
    {
        id: 'mark_absent',
        label: 'تسجيل غياب',
        icon: UserX,
        variant: 'outline',
    },
    {
        id: 'export_selected',
        label: 'تصدير المحدد',
        icon: Download,
        variant: 'outline',
    },
    {
        id: 'send_notification',
        label: 'إرسال إشعار',
        icon: MessageSquare,
        variant: 'outline',
    },
    {
        id: 'archive',
        label: 'أرشفة',
        icon: Archive,
        variant: 'outline',
    },
    {
        id: 'delete',
        label: 'حذف',
        icon: Trash2,
        variant: 'destructive',
        requiresConfirmation: true,
        confirmationMessage: 'هل أنت متأكد من حذف العناصر المحددة؟',
    },
];

export function BulkActions({
    selectedCount,
    totalCount,
    onClearSelection,
    onAction,
    actions = defaultActions,
    className,
    isRTL = true,
    compact = false,
}: BulkActionsProps) {
    if (selectedCount === 0) {
        return null;
    }

    const handleAction = (actionId: string) => {
        const action = actions.find(a => a.id === actionId);
        if (action?.requiresConfirmation) {
            if (window.confirm(action.confirmationMessage || 'هل أنت متأكد؟')) {
                onAction(actionId);
            }
        } else {
            onAction(actionId);
        }
    };

    const primaryActions = actions.slice(0, compact ? 2 : 3);
    const secondaryActions = actions.slice(compact ? 2 : 3);

    if (compact) {
        return (
            <div className={cn(
                'flex items-center gap-2 rounded-lg border bg-blue-50 px-3 py-2 text-blue-900 dark:bg-blue-950 dark:text-blue-100',
                isRTL && 'flex-row-reverse',
                className
            )}>
                <Badge variant="secondary" className="font-medium">
                    {selectedCount} محدد
                </Badge>
                
                <div className={cn(
                    'flex items-center gap-1',
                    isRTL && 'flex-row-reverse'
                )}>
                    {primaryActions.map((action) => {
                        const Icon = action.icon;
                        return (
                            <Button
                                key={action.id}
                                variant={action.variant || 'outline'}
                                size="sm"
                                onClick={() => handleAction(action.id)}
                                className="h-7 px-2"
                            >
                                <Icon className="h-3 w-3" />
                            </Button>
                        );
                    })}
                    
                    {secondaryActions.length > 0 && (
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm" className="h-7 w-7 p-0">
                                    <MoreHorizontal className="h-3 w-3" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align={isRTL ? 'end' : 'start'}>
                                {secondaryActions.map((action) => {
                                    const Icon = action.icon;
                                    return (
                                        <DropdownMenuItem
                                            key={action.id}
                                            onClick={() => handleAction(action.id)}
                                            className={cn(
                                                'gap-2',
                                                action.variant === 'destructive' && 'text-destructive'
                                            )}
                                        >
                                            <Icon className="h-4 w-4" />
                                            {action.label}
                                        </DropdownMenuItem>
                                    );
                                })}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    )}
                </div>

                <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClearSelection}
                    className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground"
                >
                    <X className="h-3 w-3" />
                </Button>
            </div>
        );
    }

    return (
        <Card className={cn(
            'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950',
            className
        )}>
            <CardContent className="p-4">
                <div className={cn(
                    'flex items-center justify-between',
                    isRTL && 'flex-row-reverse'
                )}>
                    <div className={cn(
                        'flex items-center gap-3',
                        isRTL && 'flex-row-reverse'
                    )}>
                        <div className="flex items-center gap-2">
                            <UserCheck className="h-5 w-5 text-blue-600" />
                            <span className="font-medium text-blue-800 dark:text-blue-200">
                                تم تحديد {selectedCount.toLocaleString('ar')} من {totalCount.toLocaleString('ar')} عنصر
                            </span>
                        </div>
                    </div>

                    <div className={cn(
                        'flex items-center gap-2',
                        isRTL && 'flex-row-reverse'
                    )}>
                        {/* Primary Actions */}
                        {primaryActions.map((action) => {
                            const Icon = action.icon;
                            return (
                                <Button
                                    key={action.id}
                                    variant={action.variant || 'outline'}
                                    size="sm"
                                    onClick={() => handleAction(action.id)}
                                    className={cn(
                                        'gap-2',
                                        action.variant === 'default' && 'bg-green-600 hover:bg-green-700 text-white',
                                        action.variant === 'destructive' && 'bg-red-600 hover:bg-red-700'
                                    )}
                                >
                                    <Icon className="h-4 w-4" />
                                    {action.label}
                                </Button>
                            );
                        })}

                        {/* Secondary Actions Dropdown */}
                        {secondaryActions.length > 0 && (
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="outline" size="sm">
                                        <MoreHorizontal className="h-4 w-4" />
                                        المزيد
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align={isRTL ? 'end' : 'start'}>
                                    <DropdownMenuLabel>إجراءات إضافية</DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    {secondaryActions.map((action) => {
                                        const Icon = action.icon;
                                        return (
                                            <DropdownMenuItem
                                                key={action.id}
                                                onClick={() => handleAction(action.id)}
                                                className={cn(
                                                    'gap-2',
                                                    action.variant === 'destructive' && 'text-destructive'
                                                )}
                                            >
                                                <Icon className="h-4 w-4" />
                                                {action.label}
                                            </DropdownMenuItem>
                                        );
                                    })}
                                </DropdownMenuContent>
                            </DropdownMenu>
                        )}

                        {/* Clear Selection */}
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onClearSelection}
                            className="text-muted-foreground hover:text-foreground"
                        >
                            <X className="h-4 w-4" />
                            إلغاء التحديد
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
