import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { BarChart3, Clock, Search, Server, Smartphone, TrendingUp, X } from 'lucide-react';

interface SearchMetrics {
    totalSearches: number;
    clientSearches: number;
    serverSearches: number;
    averageSearchTime: number;
    popularQueries: Array<{ query: string; count: number }>;
}

interface SearchPerformanceMonitorProps {
    metrics: SearchMetrics;
    currentSearchMode: 'client' | 'server' | 'idle';
    isVisible?: boolean;
    onClose?: () => void;
    className?: string;
}

export function SearchPerformanceMonitor({
    metrics,
    currentSearchMode,
    isVisible = false,
    onClose,
    className
}: SearchPerformanceMonitorProps) {
    const [isExpanded, setIsExpanded] = useState(false);

    if (!isVisible) return null;

    const clientPercentage = metrics.totalSearches > 0 
        ? Math.round((metrics.clientSearches / metrics.totalSearches) * 100)
        : 0;

    const serverPercentage = 100 - clientPercentage;

    return (
        <Card className={cn(
            "fixed bottom-4 right-4 z-50 w-80 shadow-lg border-2",
            currentSearchMode === 'client' && "border-green-500",
            currentSearchMode === 'server' && "border-blue-500",
            currentSearchMode === 'idle' && "border-gray-300",
            className
        )}>
            <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <BarChart3 className="h-4 w-4" />
                        مراقب أداء البحث
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <Badge 
                            variant={currentSearchMode === 'client' ? 'default' : 'secondary'}
                            className={cn(
                                "text-xs",
                                currentSearchMode === 'client' && "bg-green-100 text-green-800",
                                currentSearchMode === 'server' && "bg-blue-100 text-blue-800"
                            )}
                        >
                            {currentSearchMode === 'client' && <Smartphone className="h-3 w-3 mr-1" />}
                            {currentSearchMode === 'server' && <Server className="h-3 w-3 mr-1" />}
                            {currentSearchMode === 'client' ? 'محلي' : currentSearchMode === 'server' ? 'خادم' : 'خامل'}
                        </Badge>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsExpanded(!isExpanded)}
                            className="h-6 w-6 p-0"
                        >
                            <TrendingUp className="h-3 w-3" />
                        </Button>
                        {onClose && (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={onClose}
                                className="h-6 w-6 p-0"
                            >
                                <X className="h-3 w-3" />
                            </Button>
                        )}
                    </div>
                </div>
            </CardHeader>
            
            <CardContent className="pt-0">
                {/* Basic Metrics */}
                <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center gap-1">
                            <Search className="h-3 w-3" />
                            إجمالي البحثات
                        </span>
                        <Badge variant="outline">{metrics.totalSearches}</Badge>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            متوسط الوقت
                        </span>
                        <Badge variant="outline">{metrics.averageSearchTime.toFixed(0)}ms</Badge>
                    </div>

                    {/* Search Mode Distribution */}
                    <div className="space-y-1">
                        <div className="text-xs text-muted-foreground">توزيع أنواع البحث</div>
                        <div className="flex h-2 rounded-full overflow-hidden bg-muted">
                            <div 
                                className="bg-green-500 transition-all duration-300"
                                style={{ width: `${clientPercentage}%` }}
                            />
                            <div 
                                className="bg-blue-500 transition-all duration-300"
                                style={{ width: `${serverPercentage}%` }}
                            />
                        </div>
                        <div className="flex justify-between text-xs text-muted-foreground">
                            <span>محلي {clientPercentage}%</span>
                            <span>خادم {serverPercentage}%</span>
                        </div>
                    </div>
                </div>

                {/* Expanded Metrics */}
                {isExpanded && (
                    <div className="mt-4 pt-4 border-t space-y-3">
                        <div>
                            <div className="text-xs font-medium text-muted-foreground mb-2">
                                البحثات الشائعة
                            </div>
                            <div className="space-y-1">
                                {metrics.popularQueries.slice(0, 5).map((query, index) => (
                                    <div key={index} className="flex items-center justify-between text-xs">
                                        <span className="truncate flex-1 mr-2">{query.query}</span>
                                        <Badge variant="secondary" className="text-xs">
                                            {query.count}
                                        </Badge>
                                    </div>
                                ))}
                                {metrics.popularQueries.length === 0 && (
                                    <div className="text-xs text-muted-foreground text-center py-2">
                                        لا توجد بحثات بعد
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2 text-xs">
                            <div className="text-center p-2 bg-green-50 rounded">
                                <div className="font-medium text-green-800">{metrics.clientSearches}</div>
                                <div className="text-green-600">بحث محلي</div>
                            </div>
                            <div className="text-center p-2 bg-blue-50 rounded">
                                <div className="font-medium text-blue-800">{metrics.serverSearches}</div>
                                <div className="text-blue-600">بحث خادم</div>
                            </div>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}

interface SearchStatsProps {
    searchMode: 'client' | 'server' | 'idle';
    resultCount: number;
    searchTime?: number;
    cacheHit?: boolean;
    className?: string;
}

export function SearchStats({
    searchMode,
    resultCount,
    searchTime,
    cacheHit = false,
    className
}: SearchStatsProps) {
    return (
        <div className={cn("flex items-center gap-2 text-xs text-muted-foreground", className)}>
            <Badge 
                variant="outline" 
                className={cn(
                    "text-xs",
                    searchMode === 'client' && "border-green-500 text-green-700",
                    searchMode === 'server' && "border-blue-500 text-blue-700"
                )}
            >
                {searchMode === 'client' && <Smartphone className="h-3 w-3 mr-1" />}
                {searchMode === 'server' && <Server className="h-3 w-3 mr-1" />}
                {searchMode === 'client' ? 'محلي' : searchMode === 'server' ? 'خادم' : 'خامل'}
            </Badge>
            
            <span>{resultCount} نتيجة</span>
            
            {searchTime && (
                <span className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {searchTime}ms
                </span>
            )}
            
            {cacheHit && (
                <Badge variant="secondary" className="text-xs">
                    مخزن مؤقت
                </Badge>
            )}
        </div>
    );
}

interface SearchModeIndicatorProps {
    mode: 'client' | 'server' | 'idle';
    className?: string;
}

export function SearchModeIndicator({ mode, className }: SearchModeIndicatorProps) {
    const modeConfig = {
        client: {
            icon: Smartphone,
            label: 'بحث محلي سريع',
            color: 'text-green-600',
            bgColor: 'bg-green-100',
        },
        server: {
            icon: Server,
            label: 'بحث خادم شامل',
            color: 'text-blue-600',
            bgColor: 'bg-blue-100',
        },
        idle: {
            icon: Search,
            label: 'جاهز للبحث',
            color: 'text-gray-600',
            bgColor: 'bg-gray-100',
        },
    };

    const config = modeConfig[mode];
    const Icon = config.icon;

    return (
        <div className={cn(
            "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
            config.color,
            config.bgColor,
            className
        )}>
            <Icon className="h-3 w-3" />
            {config.label}
        </div>
    );
}
