import React from 'react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import {
    Calendar,
    GraduationCap,
    MapPin,
    Phone,
    Facebook,
    UserCheck,
    QrCode,
    Eye,
    Edit,
    Trash2,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    HoverCard,
    HoverCardContent,
    HoverCardTrigger,
} from '@/components/ui/hover-card';
import { cn } from '@/lib/utils';
import { type User as AppUser } from '@/stores/app-store';

interface UserPreviewCardProps {
    user: AppUser;
    onView?: (user: AppUser) => void;
    onEdit?: (user: AppUser) => void;
    onDelete?: (user: AppUser) => void;
    onShowQR?: (user: AppUser) => void;
    className?: string;
    isRTL?: boolean;
}

export function UserPreviewCard({
    user,
    onView,
    onEdit,
    onDelete,
    onShowQR,
    className,
    isRTL = true,
}: UserPreviewCardProps) {
    const yearNames = {
        1: 'السنة الأولى',
        2: 'السنة الثانية',
        3: 'السنة الثالثة',
        4: 'السنة الرابعة',
    };

    return (
        <HoverCard>
            <HoverCardTrigger asChild>
                <Card className={cn(
                    'group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02]',
                    className
                )}>
                    <CardContent className="p-4">
                        <div className={cn(
                            'flex items-center gap-3',
                            isRTL ? 'flex-row-reverse' : 'flex-row'
                        )}>
                            {/* Avatar */}
                            <div className="relative">
                                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold">
                                    {user.name.charAt(0)}
                                </div>
                                {user.is_active && (
                                    <div className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white" />
                                )}
                            </div>

                            {/* User Info */}
                            <div className="flex-1 min-w-0">
                                <h3 className="font-semibold text-sm truncate">{user.name}</h3>
                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <Phone className="h-3 w-3" />
                                    <span>{user.phone}</span>
                                </div>
                                <div className="flex items-center gap-1 mt-1">
                                    <Badge variant="outline" className="text-xs">
                                        {yearNames[user.year as keyof typeof yearNames]}
                                    </Badge>
                                    <Badge variant={user.gender === 'male' ? 'default' : 'secondary'} className="text-xs">
                                        {user.gender === 'male' ? 'ذكر' : 'أنثى'}
                                    </Badge>
                                </div>
                            </div>

                            {/* Quick Actions */}
                            <div className={cn(
                                'flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity',
                                isRTL ? 'flex-row-reverse' : 'flex-row'
                            )}>
                                {onView && (
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onView(user);
                                        }}
                                        className="h-8 w-8 p-0"
                                    >
                                        <Eye className="h-4 w-4" />
                                    </Button>
                                )}
                                {onEdit && (
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onEdit(user);
                                        }}
                                        className="h-8 w-8 p-0"
                                    >
                                        <Edit className="h-4 w-4" />
                                    </Button>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </HoverCardTrigger>
            
            <HoverCardContent className="w-80" align={isRTL ? 'end' : 'start'}>
                <div className="space-y-4">
                    {/* Header */}
                    <div className={cn(
                        'flex items-center gap-3',
                        isRTL ? 'flex-row-reverse' : 'flex-row'
                    )}>
                        <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xl font-bold">
                            {user.name.charAt(0)}
                        </div>
                        <div className="flex-1">
                            <h3 className="font-bold text-lg">{user.name}</h3>
                            <p className="text-sm text-muted-foreground">
                                {yearNames[user.year as keyof typeof yearNames]}
                            </p>
                        </div>
                    </div>

                    {/* Details Grid */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4 text-muted-foreground" />
                                <span>{user.phone}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <GraduationCap className="h-4 w-4 text-muted-foreground" />
                                <span>{user.college}</span>
                            </div>
                            {user.department && (
                                <div className="flex items-center gap-2">
                                    <span className="text-muted-foreground">القسم:</span>
                                    <span>{user.department}</span>
                                </div>
                            )}
                        </div>
                        
                        <div className="space-y-2">
                            {user.birthdate && (
                                <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                    <span>{format(new Date(user.birthdate), 'dd/MM/yyyy', { locale: ar })}</span>
                                </div>
                            )}
                            {user.address && (
                                <div className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4 text-muted-foreground" />
                                    <span className="truncate">{user.address}</span>
                                </div>
                            )}
                            {user.facebook_url && (
                                <div className="flex items-center gap-2">
                                    <Facebook className="h-4 w-4 text-muted-foreground" />
                                    <span>Facebook</span>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Status Badges */}
                    <div className="flex flex-wrap gap-2">
                        <Badge variant={user.is_active ? 'default' : 'secondary'}>
                            {user.is_active ? 'نشط' : 'غير نشط'}
                        </Badge>
                        <Badge variant={user.gender === 'male' ? 'default' : 'secondary'}>
                            {user.gender === 'male' ? 'ذكر' : 'أنثى'}
                        </Badge>
                        {user.first_attendance_date && (
                            <Badge variant="outline">
                                <UserCheck className="h-3 w-3 mr-1" />
                                لديه حضور
                            </Badge>
                        )}
                        {user.qr_token && (
                            <Badge variant="outline">
                                <QrCode className="h-3 w-3 mr-1" />
                                QR متاح
                            </Badge>
                        )}
                    </div>

                    {/* Actions */}
                    <div className={cn(
                        'flex gap-2 pt-2 border-t',
                        isRTL ? 'flex-row-reverse' : 'flex-row'
                    )}>
                        {onView && (
                            <Button variant="outline" size="sm" onClick={() => onView(user)} className="flex-1">
                                <Eye className="h-4 w-4 mr-2" />
                                عرض التفاصيل
                            </Button>
                        )}
                        {onShowQR && user.qr_token && (
                            <Button variant="outline" size="sm" onClick={() => onShowQR(user)}>
                                <QrCode className="h-4 w-4" />
                            </Button>
                        )}
                        {onEdit && (
                            <Button variant="outline" size="sm" onClick={() => onEdit(user)}>
                                <Edit className="h-4 w-4" />
                            </Button>
                        )}
                    </div>
                </div>
            </HoverCardContent>
        </HoverCard>
    );
}
