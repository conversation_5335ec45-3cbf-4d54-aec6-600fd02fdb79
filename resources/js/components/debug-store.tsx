import { useAppStore } from '@/stores/app-store';
import { useEffect } from 'react';

export function DebugStore() {
    const { users, attendanceRecords, initialized, initializeWithMockData } = useAppStore();

    useEffect(() => {
        console.log('DebugStore - Store State:', {
            initialized,
            usersCount: users.length,
            attendanceCount: attendanceRecords.length,
            sampleUsers: users.slice(0, 3).map(u => ({
                id: u.id,
                name: u.name,
                year: u.year,
                college: u.college
            }))
        });
    }, [users, attendanceRecords, initialized]);

    const handleForceInit = () => {
        console.log('Force initializing mock data...');
        initializeWithMockData();
    };

    return (
        <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg z-50 max-w-sm">
            <h3 className="font-bold text-sm mb-2">Debug Store</h3>
            <div className="text-xs space-y-1">
                <div>Initialized: {initialized ? 'Yes' : 'No'}</div>
                <div>Users: {users.length}</div>
                <div>Attendance: {attendanceRecords.length}</div>
                <div>Year 1 Users: {users.filter(u => u.year === 1).length}</div>
                <div>Year 2 Users: {users.filter(u => u.year === 2).length}</div>
                <div>Year 3 Users: {users.filter(u => u.year === 3).length}</div>
                <div>Year 4 Users: {users.filter(u => u.year === 4).length}</div>
            </div>
            <button 
                onClick={handleForceInit}
                className="mt-2 px-2 py-1 bg-blue-500 text-white text-xs rounded"
            >
                Force Init
            </button>
        </div>
    );
}
