/**
 * Search Validation Utilities
 * 
 * This file contains utilities to validate and test search functionality
 * in the browser environment.
 */

import { searchService } from '@/lib/search';
import { User } from '@/stores/app-store';

interface SearchValidationResult {
    testName: string;
    passed: boolean;
    message: string;
    duration?: number;
    details?: any;
}

interface SearchValidationSuite {
    results: SearchValidationResult[];
    totalTests: number;
    passedTests: number;
    failedTests: number;
    totalDuration: number;
}

/**
 * Validates search functionality with a given dataset
 */
export class SearchValidator {
    private users: User[] = [];
    private results: SearchValidationResult[] = [];

    constructor(users: User[]) {
        this.users = users;
        this.initializeSearch();
    }

    private initializeSearch() {
        try {
            searchService.initializeUsers(this.users);
            this.addResult('Search Service Initialization', true, 'Search service initialized successfully');
        } catch (error) {
            this.addResult('Search Service Initialization', false, `Failed to initialize: ${error}`);
        }
    }

    private addResult(testName: string, passed: boolean, message: string, duration?: number, details?: any) {
        this.results.push({
            testName,
            passed,
            message,
            duration,
            details
        });
    }

    private measureTime<T>(fn: () => T): { result: T; duration: number } {
        const start = performance.now();
        const result = fn();
        const duration = performance.now() - start;
        return { result, duration };
    }

    /**
     * Test basic search functionality
     */
    testBasicSearch(): SearchValidationResult {
        const { result, duration } = this.measureTime(() => {
            return searchService.searchUsers('test');
        });

        const passed = Array.isArray(result);
        const message = passed 
            ? `Basic search returned ${result.length} results in ${duration.toFixed(2)}ms`
            : 'Basic search failed to return array';

        this.addResult('Basic Search', passed, message, duration, { resultCount: result.length });
        return this.results[this.results.length - 1];
    }

    /**
     * Test search performance with different query lengths
     */
    testSearchPerformance(): SearchValidationResult[] {
        const queries = ['a', 'ab', 'abc', 'test query', 'longer test query with multiple words'];
        const performanceResults: SearchValidationResult[] = [];

        queries.forEach(query => {
            const { result, duration } = this.measureTime(() => {
                return searchService.searchUsers(query);
            });

            const passed = duration < 100; // Should complete within 100ms
            const message = `Search for "${query}" took ${duration.toFixed(2)}ms (${result.length} results)`;

            const testResult: SearchValidationResult = {
                testName: `Performance Test - "${query}"`,
                passed,
                message,
                duration,
                details: { query, resultCount: result.length }
            };

            performanceResults.push(testResult);
            this.results.push(testResult);
        });

        return performanceResults;
    }

    /**
     * Test search with Arabic text
     */
    testArabicSearch(): SearchValidationResult {
        const arabicQueries = ['محمد', 'أحمد', 'فاطمة', 'كلية', 'هندسة'];
        let totalResults = 0;
        let totalDuration = 0;

        const { result: allResults, duration } = this.measureTime(() => {
            return arabicQueries.map(query => {
                const results = searchService.searchUsers(query);
                totalResults += results.length;
                return results;
            });
        });

        totalDuration = duration;

        const passed = allResults.every(results => Array.isArray(results));
        const message = passed
            ? `Arabic search tests passed. Total results: ${totalResults}, Duration: ${totalDuration.toFixed(2)}ms`
            : 'Arabic search tests failed';

        this.addResult('Arabic Search', passed, message, totalDuration, { 
            queries: arabicQueries, 
            totalResults 
        });
        return this.results[this.results.length - 1];
    }

    /**
     * Test search highlighting
     */
    testSearchHighlighting(): SearchValidationResult {
        const { result, duration } = this.measureTime(() => {
            return searchService.searchUsers('test');
        });

        let highlightedCount = 0;
        result.forEach(searchResult => {
            if (searchResult.highlighted && Object.keys(searchResult.highlighted).length > 0) {
                highlightedCount++;
            }
        });

        const passed = result.length === 0 || highlightedCount > 0;
        const message = passed
            ? `Highlighting test passed. ${highlightedCount}/${result.length} results have highlighting`
            : 'Highlighting test failed - no highlighted results found';

        this.addResult('Search Highlighting', passed, message, duration, { 
            totalResults: result.length, 
            highlightedResults: highlightedCount 
        });
        return this.results[this.results.length - 1];
    }

    /**
     * Test search with filters
     */
    testSearchFilters(): SearchValidationResult {
        const filters = [
            { gender: 'male' },
            { gender: 'female' },
            { year: 1 },
            { college: 'هندسة' }
        ];

        let allPassed = true;
        const filterResults: any[] = [];

        const { result: _, duration } = this.measureTime(() => {
            filters.forEach(filter => {
                const results = searchService.searchUsers('', filter);
                filterResults.push({ filter, resultCount: results.length });
                
                // Validate filter application
                if (filter.gender) {
                    const invalidResults = results.filter(r => r.item.gender !== filter.gender);
                    if (invalidResults.length > 0) {
                        allPassed = false;
                    }
                }
            });
        });

        const message = allPassed
            ? `Filter tests passed. Tested ${filters.length} different filters in ${duration.toFixed(2)}ms`
            : 'Some filter tests failed';

        this.addResult('Search Filters', allPassed, message, duration, { filterResults });
        return this.results[this.results.length - 1];
    }

    /**
     * Test search suggestions
     */
    testSearchSuggestions(): SearchValidationResult {
        const queries = ['م', 'أ', 'ك', 'test'];
        let totalSuggestions = 0;

        const { result: _, duration } = this.measureTime(() => {
            queries.forEach(query => {
                const suggestions = searchService.getUserSuggestions(query, 5);
                totalSuggestions += suggestions.length;
            });
        });

        const passed = totalSuggestions > 0;
        const message = passed
            ? `Suggestions test passed. Generated ${totalSuggestions} suggestions for ${queries.length} queries`
            : 'Suggestions test failed - no suggestions generated';

        this.addResult('Search Suggestions', passed, message, duration, { 
            queries, 
            totalSuggestions 
        });
        return this.results[this.results.length - 1];
    }

    /**
     * Run all validation tests
     */
    runAllTests(): SearchValidationSuite {
        console.log('🔍 Starting search validation tests...');

        this.testBasicSearch();
        this.testSearchPerformance();
        this.testArabicSearch();
        this.testSearchHighlighting();
        this.testSearchFilters();
        this.testSearchSuggestions();

        const suite: SearchValidationSuite = {
            results: this.results,
            totalTests: this.results.length,
            passedTests: this.results.filter(r => r.passed).length,
            failedTests: this.results.filter(r => !r.passed).length,
            totalDuration: this.results.reduce((sum, r) => sum + (r.duration || 0), 0)
        };

        console.log('✅ Search validation completed:', suite);
        return suite;
    }

    /**
     * Generate a validation report
     */
    generateReport(): string {
        const suite = this.runAllTests();
        
        let report = `
# Search Functionality Validation Report

## Summary
- **Total Tests**: ${suite.totalTests}
- **Passed**: ${suite.passedTests}
- **Failed**: ${suite.failedTests}
- **Success Rate**: ${((suite.passedTests / suite.totalTests) * 100).toFixed(1)}%
- **Total Duration**: ${suite.totalDuration.toFixed(2)}ms

## Test Results

`;

        suite.results.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            const duration = result.duration ? ` (${result.duration.toFixed(2)}ms)` : '';
            report += `${status} **${result.testName}**${duration}\n`;
            report += `   ${result.message}\n\n`;
        });

        return report;
    }
}

/**
 * Quick validation function for browser console
 */
export function validateSearch(users: User[]): SearchValidationSuite {
    const validator = new SearchValidator(users);
    return validator.runAllTests();
}

/**
 * Performance benchmark for search functionality
 */
export function benchmarkSearch(users: User[], iterations: number = 100): any {
    console.log(`🚀 Running search benchmark with ${iterations} iterations...`);
    
    const queries = ['test', 'محمد', 'أحمد', 'كلية', 'هندسة'];
    const results: any = {};

    searchService.initializeUsers(users);

    queries.forEach(query => {
        const times: number[] = [];
        
        for (let i = 0; i < iterations; i++) {
            const start = performance.now();
            searchService.searchUsers(query);
            const end = performance.now();
            times.push(end - start);
        }

        results[query] = {
            min: Math.min(...times),
            max: Math.max(...times),
            avg: times.reduce((sum, time) => sum + time, 0) / times.length,
            median: times.sort((a, b) => a - b)[Math.floor(times.length / 2)]
        };
    });

    console.log('📊 Benchmark results:', results);
    return results;
}

// Export for global access in browser console
if (typeof window !== 'undefined') {
    (window as any).validateSearch = validateSearch;
    (window as any).benchmarkSearch = benchmarkSearch;
}
