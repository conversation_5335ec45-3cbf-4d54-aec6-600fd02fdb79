'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/auth-context';
import { useRTL } from '@/contexts/rtl-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Link } from '@inertiajs/react';
import { Eye, EyeOff, Lock, LogIn, Mail, Shield, Sparkles, Users } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

const loginSchema = z.object({
    email: z.string().email('البريد الإلكتروني غير صحيح'),
    password: z.string().min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
    remember: z.boolean().default(false),
});

type LoginData = z.infer<typeof loginSchema>;

function LoginPage() {
    const { login } = useAuth();
    const { isRTL, direction } = useRTL();
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const form = useForm<LoginData>({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: '',
            password: '',
            remember: false,
        },
    });

    const handleSubmit = async (data: LoginData) => {
        setIsLoading(true);
        try {
            await login(data.email, data.password, data.remember);
            // Redirect will be handled by the auth context
        } catch (error) {
            console.error('Login error:', error);
            form.setError('email', {
                type: 'manual',
                message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div
            className="font-cairo flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-50 via-white to-indigo-50 p-4"
            dir={direction}
        >
            <div className="w-full max-w-md">
                {/* Header */}
                <div className="animate-fade-in mb-8 text-center">
                    <div className="mb-4 inline-flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 shadow-lg">
                        <Users className="h-10 w-10 text-white" />
                    </div>
                    <h1 className="mb-2 text-3xl font-bold text-gray-900">نظام حضور الشباب</h1>
                    <p className="flex items-center justify-center gap-2 text-gray-600">
                        <Shield className="h-4 w-4" />
                        إدارة حضور الأعضاء بكفاءة وأمان
                    </p>
                </div>

                {/* Login Card */}
                <Card className="animate-scale-in border-0 bg-white/80 shadow-xl backdrop-blur">
                    <CardHeader className="pb-2 text-center">
                        <CardTitle className="flex items-center justify-center gap-2 text-2xl font-bold text-gray-900">
                            <LogIn className="h-6 w-6 text-blue-600" />
                            تسجيل الدخول
                        </CardTitle>
                        <CardDescription className="text-gray-600">أدخل بياناتك للوصول إلى النظام</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name="email"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2 text-gray-700">
                                                <Mail className="h-4 w-4" />
                                                البريد الإلكتروني
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="email"
                                                    placeholder="أدخل بريدك الإلكتروني"
                                                    className="h-12 border-gray-200 bg-gray-50/50 text-right transition-all focus:border-blue-500 focus:bg-white"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="password"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2 text-gray-700">
                                                <Lock className="h-4 w-4" />
                                                كلمة المرور
                                            </FormLabel>
                                            <FormControl>
                                                <div className="relative">
                                                    <Input
                                                        type={showPassword ? 'text' : 'password'}
                                                        placeholder="أدخل كلمة المرور"
                                                        className="h-12 border-gray-200 bg-gray-50/50 pr-12 text-right transition-all focus:border-blue-500 focus:bg-white"
                                                        {...field}
                                                    />
                                                    <button
                                                        type="button"
                                                        onClick={() => setShowPassword(!showPassword)}
                                                        className="absolute top-1/2 left-3 -translate-y-1/2 transform text-gray-400 transition-colors hover:text-gray-600"
                                                    >
                                                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                                    </button>
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="remember"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                                            <FormControl>
                                                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                                            </FormControl>
                                            <div className="space-y-1 leading-none">
                                                <FormLabel className="cursor-pointer text-sm text-gray-600">تذكرني على هذا الجهاز</FormLabel>
                                            </div>
                                        </FormItem>
                                    )}
                                />

                                <Button
                                    type="submit"
                                    className="h-12 w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-lg font-medium shadow-lg transition-all duration-300 hover:from-blue-600 hover:to-indigo-700 hover:shadow-xl"
                                    disabled={isLoading}
                                >
                                    {isLoading ? (
                                        <div className="flex items-center gap-2">
                                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                                            جاري تسجيل الدخول...
                                        </div>
                                    ) : (
                                        <div className="flex items-center gap-2">
                                            <LogIn className="h-5 w-5" />
                                            دخول
                                        </div>
                                    )}
                                </Button>
                            </form>
                        </Form>

                        {/* Divider */}
                        <div className="relative">
                            <div className="absolute inset-0 flex items-center">
                                <span className="w-full border-t border-gray-200" />
                            </div>
                            <div className="relative flex justify-center text-xs uppercase">
                                <span className="bg-white px-2 text-gray-500">أو</span>
                            </div>
                        </div>

                        {/* Demo Login */}
                        <div className="space-y-3">
                            <p className="text-center text-xs text-gray-500">للتجربة، يمكنك استخدام:</p>
                            <div className="rounded-lg bg-gray-50 p-3 text-sm">
                                <div className="mb-1 flex items-center justify-between">
                                    <span className="text-gray-600">البريد:</span>
                                    <span className="font-mono"><EMAIL></span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-gray-600">كلمة المرور:</span>
                                    <span className="font-mono">password</span>
                                </div>
                            </div>
                            <Button
                                variant="outline"
                                className="w-full"
                                onClick={() => {
                                    form.setValue('email', '<EMAIL>');
                                    form.setValue('password', 'password');
                                }}
                            >
                                <Sparkles className="mr-2 h-4 w-4" />
                                ملء البيانات التجريبية
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Footer */}
                <div className="animate-fade-in mt-8 text-center">
                    <p className="text-sm text-gray-500">
                        هل تواجه مشكلة في تسجيل الدخول؟{' '}
                        <Link href="/contact" className="font-medium text-blue-600 hover:text-blue-700">
                            اتصل بالدعم
                        </Link>
                    </p>
                    <div className="mt-4 flex items-center justify-center gap-4 text-xs text-gray-400">
                        <span>© 2024 نظام حضور الشباب</span>
                        <span>•</span>
                        <span>جميع الحقوق محفوظة</span>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default LoginPage;
