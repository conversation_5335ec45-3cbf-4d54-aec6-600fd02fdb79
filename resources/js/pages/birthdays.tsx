'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useRTL } from '@/contexts/rtl-context';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { useAppStore } from '@/stores/app-store';
import { addDays, endOfMonth, endOfWeek, format, isSameDay, startOfMonth, startOfWeek } from 'date-fns';
import { ar } from 'date-fns/locale';
import { Cake, Calendar, Download, Filter, Gift, GraduationCap, MessageCircle, Phone, Search, Users } from 'lucide-react';
import { useMemo, useState } from 'react';

function BirthdaysPage() {
    const { users } = useAppStore();
    const { isRTL, direction } = useRTL();

    const [searchTerm, setSearchTerm] = useState('');
    const [filterYear, setFilterYear] = useState('all');
    const [filterMonth, setFilterMonth] = useState('all');
    const [activeTab, setActiveTab] = useState('today');

    const today = new Date();

    // Filter users based on search and filters
    const filteredUsers = useMemo(() => {
        return users.filter((user) => {
            const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesYear = filterYear === 'all' || user.year.toString() === filterYear;

            let matchesMonth = true;
            if (filterMonth !== 'all') {
                const userBirthMonth = new Date(user.birthdate).getMonth() + 1;
                matchesMonth = userBirthMonth.toString() === filterMonth;
            }

            return matchesSearch && matchesYear && matchesMonth;
        });
    }, [users, searchTerm, filterYear, filterMonth]);

    // Get birthdays for different time periods
    const getBirthdaysForPeriod = (period: string) => {
        const currentYear = today.getFullYear();

        return filteredUsers
            .map((user) => {
                const birthDate = new Date(user.birthdate);
                const thisYearBirthday = new Date(currentYear, birthDate.getMonth(), birthDate.getDate());
                const nextYearBirthday = new Date(currentYear + 1, birthDate.getMonth(), birthDate.getDate());

                // Calculate age
                const age = currentYear - birthDate.getFullYear();

                return {
                    ...user,
                    thisYearBirthday,
                    nextYearBirthday,
                    age,
                };
            })
            .filter((user) => {
                const { thisYearBirthday, nextYearBirthday } = user;

                switch (period) {
                    case 'today':
                        return isSameDay(thisYearBirthday, today) || isSameDay(nextYearBirthday, today);
                    case 'week':
                        const weekStart = startOfWeek(today, { weekStartsOn: 1 });
                        const weekEnd = endOfWeek(today, { weekStartsOn: 1 });
                        return (
                            (thisYearBirthday >= weekStart && thisYearBirthday <= weekEnd) ||
                            (nextYearBirthday >= weekStart && nextYearBirthday <= weekEnd)
                        );
                    case 'month':
                        const monthStart = startOfMonth(today);
                        const monthEnd = endOfMonth(today);
                        return (
                            (thisYearBirthday >= monthStart && thisYearBirthday <= monthEnd) ||
                            (nextYearBirthday >= monthStart && nextYearBirthday <= monthEnd)
                        );
                    case 'upcoming':
                        const next30Days = addDays(today, 30);
                        return (
                            (thisYearBirthday >= today && thisYearBirthday <= next30Days) ||
                            (nextYearBirthday >= today && nextYearBirthday <= next30Days)
                        );
                    default:
                        return true;
                }
            })
            .sort((a, b) => {
                const aDate = a.thisYearBirthday >= today ? a.thisYearBirthday : a.nextYearBirthday;
                const bDate = b.thisYearBirthday >= today ? b.thisYearBirthday : b.nextYearBirthday;
                return aDate.getTime() - bDate.getTime();
            });
    };

    const sendWhatsAppMessage = (user: any) => {
        const message = `🎉 عيد ميلاد سعيد ${user.name}! 🎂\n\nنتمنى لك عامًا جديدًا مليئًا بالفرح والنجاح والبركات.`;
        const phoneNumber = user.phone.replace(/[^0-9]/g, '');
        const whatsappUrl = `https://wa.me/+2${phoneNumber}?text=${encodeURIComponent(message)}`;
        window.open(whatsappUrl, '_blank');
    };

    const exportBirthdays = () => {
        const allBirthdays = getBirthdaysForPeriod('month');
        const csv = [
            ['الاسم', 'تاريخ الميلاد', 'العمر', 'السنة الدراسية', 'الكلية', 'رقم الهاتف'].join(','),
            ...allBirthdays.map((user) =>
                [user.name, format(new Date(user.birthdate), 'yyyy-MM-dd'), user.age, user.year, user.college, user.phone].join(','),
            ),
        ].join('\n');

        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `birthdays-${format(today, 'yyyy-MM')}.csv`;
        link.click();
    };

    const getBirthdayStatus = (birthdate: string) => {
        const birthDate = new Date(birthdate);
        const currentYear = today.getFullYear();
        const thisYearBirthday = new Date(currentYear, birthDate.getMonth(), birthDate.getDate());

        if (isSameDay(thisYearBirthday, today)) {
            return { status: 'today', label: 'اليوم', color: 'bg-red-100 text-red-700' };
        } else if (thisYearBirthday < today) {
            const nextYearBirthday = new Date(currentYear + 1, birthDate.getMonth(), birthDate.getDate());
            const daysUntil = Math.ceil((nextYearBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
            return { status: 'upcoming', label: `خلال ${daysUntil} يوم`, color: 'bg-blue-100 text-blue-700' };
        } else {
            const daysUntil = Math.ceil((thisYearBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
            return { status: 'upcoming', label: `خلال ${daysUntil} يوم`, color: 'bg-blue-100 text-blue-700' };
        }
    };

    const months = [
        { value: '1', label: 'يناير' },
        { value: '2', label: 'فبراير' },
        { value: '3', label: 'مارس' },
        { value: '4', label: 'أبريل' },
        { value: '5', label: 'مايو' },
        { value: '6', label: 'يونيو' },
        { value: '7', label: 'يوليو' },
        { value: '8', label: 'أغسطس' },
        { value: '9', label: 'سبتمبر' },
        { value: '10', label: 'أكتوبر' },
        { value: '11', label: 'نوفمبر' },
        { value: '12', label: 'ديسمبر' },
    ];

    return (
        <div className="space-y-6 p-6 font-cairo" dir={direction}>
            {/* Header */}
            <div className="animate-fade-in">
                <div className={cn('flex items-center justify-between', 'flex-row')}>
                    <div>
                        <div className={cn('mb-2 flex items-center gap-3', 'flex-row')}>
                            <div className="rounded-lg bg-gradient-to-r from-pink-500 to-purple-500 p-2">
                                <Gift className="h-6 w-6 text-white" />
                            </div>
                            <h1 className="text-heading gradient-text text-4xl font-bold">أعياد الميلاد</h1>
                            <Cake className="h-6 w-6 animate-pulse text-yellow-500" />
                        </div>
                        <p className={cn('text-body text-lg text-gray-600', isRTL ? 'text-right' : 'text-left')}>
                            متابعة أعياد ميلاد الأعضاء وإرسال التهاني
                        </p>
                    </div>

                    <div className="flex gap-2">
                        <Button variant="outline" onClick={exportBirthdays}>
                            <Download className="mr-2 h-4 w-4" />
                            تصدير
                        </Button>
                    </div>
                </div>
            </div>

            {/* Statistics */}
            <div className="animate-scale-in grid grid-cols-1 gap-4 md:grid-cols-4">
                <Card className="hover-lift">
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">اليوم</p>
                                <p className="text-2xl font-bold text-red-600">{getBirthdaysForPeriod('today').length}</p>
                            </div>
                            <Cake className="h-8 w-8 text-red-500" />
                        </div>
                    </CardContent>
                </Card>

                <Card className="hover-lift">
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">هذا الأسبوع</p>
                                <p className="text-2xl font-bold text-blue-600">{getBirthdaysForPeriod('week').length}</p>
                            </div>
                            <Calendar className="h-8 w-8 text-blue-500" />
                        </div>
                    </CardContent>
                </Card>

                <Card className="hover-lift">
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">هذا الشهر</p>
                                <p className="text-2xl font-bold text-green-600">{getBirthdaysForPeriod('month').length}</p>
                            </div>
                            <Gift className="h-8 w-8 text-green-500" />
                        </div>
                    </CardContent>
                </Card>

                <Card className="hover-lift">
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">الشهر القادم</p>
                                <p className="text-2xl font-bold text-purple-600">{getBirthdaysForPeriod('upcoming').length}</p>
                            </div>
                            <Users className="h-8 w-8 text-purple-500" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Filters */}
            <Card className="animate-scale-in">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Filter className="h-5 w-5" />
                        البحث والتصفية
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                        <div className="relative">
                            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                            <Input
                                placeholder="البحث بالاسم..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                        <Select value={filterYear} onValueChange={setFilterYear}>
                            <SelectTrigger>
                                <SelectValue placeholder="السنة الدراسية" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">جميع السنوات</SelectItem>
                                <SelectItem value="1">السنة الأولى</SelectItem>
                                <SelectItem value="2">السنة الثانية</SelectItem>
                                <SelectItem value="3">السنة الثالثة</SelectItem>
                                <SelectItem value="4">السنة الرابعة</SelectItem>
                            </SelectContent>
                        </Select>
                        <Select value={filterMonth} onValueChange={setFilterMonth}>
                            <SelectTrigger>
                                <SelectValue placeholder="الشهر" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">جميع الشهور</SelectItem>
                                {months.map((month) => (
                                    <SelectItem key={month.value} value={month.value}>
                                        {month.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Button
                            variant="outline"
                            onClick={() => {
                                setSearchTerm('');
                                setFilterYear('all');
                                setFilterMonth('all');
                            }}
                        >
                            إعادة تعيين
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Birthday Lists */}
            <Card className="animate-slide-in-right">
                <CardContent className="p-0">
                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                        <div className="p-6 pb-0">
                            <TabsList className="grid w-full grid-cols-4">
                                <TabsTrigger value="today">اليوم ({getBirthdaysForPeriod('today').length})</TabsTrigger>
                                <TabsTrigger value="week">هذا الأسبوع ({getBirthdaysForPeriod('week').length})</TabsTrigger>
                                <TabsTrigger value="month">هذا الشهر ({getBirthdaysForPeriod('month').length})</TabsTrigger>
                                <TabsTrigger value="upcoming">القادمة ({getBirthdaysForPeriod('upcoming').length})</TabsTrigger>
                            </TabsList>
                        </div>

                        {['today', 'week', 'month', 'upcoming'].map((period) => (
                            <TabsContent key={period} value={period} className="p-6 pt-4">
                                <div className="space-y-4">
                                    {getBirthdaysForPeriod(period).map((user) => {
                                        const birthdayStatus = getBirthdayStatus(user.birthdate);
                                        const displayBirthday = user.thisYearBirthday >= today ? user.thisYearBirthday : user.nextYearBirthday;

                                        return (
                                            <Card key={user.id} className="transition-shadow hover:shadow-md">
                                                <CardContent className="p-4">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-4">
                                                            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-pink-500 to-purple-500">
                                                                <span className="text-lg font-bold text-white">{user.name.charAt(0)}</span>
                                                            </div>
                                                            <div>
                                                                <h3 className="text-lg font-semibold">{user.name}</h3>
                                                                <div className="flex items-center gap-4 text-sm text-gray-600">
                                                                    <div className="flex items-center gap-1">
                                                                        <Calendar className="h-3 w-3" />
                                                                        <span>{format(displayBirthday, 'dd MMMM', { locale: ar })}</span>
                                                                    </div>
                                                                    <div className="flex items-center gap-1">
                                                                        <Cake className="h-3 w-3" />
                                                                        <span>
                                                                            {user.age + (displayBirthday.getFullYear() > today.getFullYear() ? 1 : 0)}{' '}
                                                                            سنة
                                                                        </span>
                                                                    </div>
                                                                    <div className="flex items-center gap-1">
                                                                        <GraduationCap className="h-3 w-3" />
                                                                        <span>السنة {user.year}</span>
                                                                    </div>
                                                                </div>
                                                                <div className="mt-1 flex items-center gap-2 text-sm text-gray-500">
                                                                    <span>{user.college}</span>
                                                                    <span>•</span>
                                                                    <Phone className="h-3 w-3" />
                                                                    <span>{user.phone}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="flex items-center gap-3">
                                                            <Badge className={birthdayStatus.color}>{birthdayStatus.label}</Badge>
                                                            <Button
                                                                size="sm"
                                                                onClick={() => sendWhatsAppMessage(user)}
                                                                className="bg-green-600 hover:bg-green-700"
                                                            >
                                                                <MessageCircle className="mr-2 h-4 w-4" />
                                                                تهنئة
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        );
                                    })}
                                    {getBirthdaysForPeriod(period).length === 0 && (
                                        <div className="py-12 text-center">
                                            <Gift className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                                            <h3 className="mb-2 text-lg font-medium text-gray-500">لا توجد أعياد ميلاد</h3>
                                            <p className="text-gray-400">لا توجد أعياد ميلاد في هذه الفترة</p>
                                        </div>
                                    )}
                                </div>
                            </TabsContent>
                        ))}
                    </Tabs>
                </CardContent>
            </Card>
        </div>
    );
}

BirthdaysPage.layout = (page: React.ReactElement) => <AppLayout children={page} />;

export default BirthdaysPage;
