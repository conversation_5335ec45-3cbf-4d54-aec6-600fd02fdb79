'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DataTable, SortableHeader } from '@/components/ui/data-table';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EnhancedSearchInput } from '@/components/ui/enhanced-search-input';
import { useRTL } from '@/contexts/rtl-context';
import { useEnhancedSearch } from '@/hooks/use-enhanced-search';
import { useUsersQuery } from '@/hooks/use-users-query';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { useAppStore, type User as AppUser } from '@/stores/app-store';
import { getCoreRowModel, getFilteredRowModel, getSortedRowModel, useReactTable, type ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import {
    Calendar,
    CheckCircle,
    Download,
    Edit,
    Eye,
    Facebook,
    GraduationCap,
    Keyboard,
    MapPin,
    MoreHorizontal,
    Phone,
    Plus,
    QrCode,
    Trash2,
    Upload,
    UserCheck,
    Users,
    X,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

// Import enhanced components
import { AdvancedFilters } from '@/components/ui/advanced-filters';
import { EnhancedPagination } from '@/components/ui/enhanced-pagination';
import { SearchPerformanceMonitor } from '@/components/ui/search-performance-monitor';
import { UserHoverCard } from '@/components/ui/user-hover-card';
import { VirtualTable } from '@/components/ui/virtual-table';
import { useFilterPersistence } from '@/hooks/use-filter-persistence';

interface SearchFilters {
    search: string;
    department?: string;
    college?: string;
    gender?: 'male' | 'female';
    hasAttendance?: boolean;
    isActive?: boolean;
}

interface UsersPageProps {
    year?: 1 | 2 | 3 | 4;
}

function UsersPage({ year = 1 }: UsersPageProps) {
    const { deleteUser, setSelectedUser, exportUsers, bulkMarkAttendance } = useAppStore();
    const { isRTL, direction } = useRTL();

    // Enhanced search and filter state with persistence
    const defaultFilters: SearchFilters = {
        search: '',
        department: undefined,
        college: undefined,
        gender: undefined,
        hasAttendance: undefined,
        isActive: true,
    };

    const {
        filters,
        setFilters: setPersistedFilters,
        clearFilters: clearPersistedFilters,
    } = useFilterPersistence({
        key: `users_year_${year}`,
        defaultFilters,
        storageType: 'localStorage',
        expiration: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Table state management
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(50);
    const [sortBy, setSortBy] = useState<'name' | 'academic_year' | 'created_at' | 'birth_date' | 'first_attendance_date'>('name');
    const [sortDir, setSortDir] = useState<'asc' | 'desc'>('asc');

    // UI state
    const [showQRModal, setShowQRModal] = useState<AppUser | null>(null);
    const [showUserDetails, setShowUserDetails] = useState<AppUser | null>(null);
    const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
    const [enableVirtualScrolling, setEnableVirtualScrolling] = useState(false);
    const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false);

    // Enhanced search state
    const [recentSearches, setRecentSearches] = useState<string[]>(() => {
        try {
            const stored = localStorage.getItem(`recent_searches_users_year_${year}`);
            return stored ? JSON.parse(stored) : [];
        } catch {
            return [];
        }
    });
    const [searchSuggestions, setSearchSuggestions] = useState<Array<{ text: string; type: 'recent' | 'suggestion' | 'popular'; count?: number }>>(
        [],
    );

    // TanStack Query for data fetching
    const {
        data: users,
        pagination,
        summary,
        filterOptions,
        queryInfo,
        isLoading,
        isFetching,
        isError,
        error,
        refetch,
        prefetchNextPage,
    } = useUsersQuery({
        year,
        search: filters.search,
        department: filters.department,
        college: filters.college,
        gender: filters.gender,
        hasAttendance: filters.hasAttendance,
        isActive: filters.isActive,
        page: currentPage,
        pageSize,
        sortBy,
        sortDir,
    });

    // Hybrid search with client-side fuzzy search and server fallback
    const hybridSearch = useHybridSearch(users || [], {
        serverSearchFn: (query: string) => {
            setPersistedFilters((prev) => ({ ...prev, search: query }));
            setCurrentPage(1);
        },
        debounceMs: 300,
        clientSearchThreshold: 200, // Use client search for <= 200 users
        minSearchLength: 1,
        enableClientSearch: true,
    });

    // Enhanced search suggestions based on current data
    const dynamicSuggestions = useSearchSuggestions(users || [], hybridSearch.searchQuery, 8);

    // Search analytics for performance monitoring
    const { searchMetrics, recordSearch } = useSearchAnalytics();

    // Enhanced search with caching and memoization (declare before useEffect hooks)
    const enhancedSearch = useEnhancedSearch({
        onSearch: (query: string) => {
            setPersistedFilters((prev) => ({ ...prev, search: query }));
            setCurrentPage(1);

            // Add to recent searches if it's a meaningful search
            if (query.trim().length >= 2) {
                handleAddRecentSearch(query.trim());
            }
        },
        debounceMs: 500, // Increased to 500ms to reduce API calls
        cacheTimeout: 5 * 60 * 1000, // 5 minutes cache
        minSearchLength: 1,
        enableCache: true,
    });

    // Handle recent searches
    const handleAddRecentSearch = useCallback(
        (searchTerm: string) => {
            setRecentSearches((prev) => {
                const filtered = prev.filter((term) => term !== searchTerm);
                const updated = [searchTerm, ...filtered].slice(0, 5); // Keep only 5 recent searches

                try {
                    localStorage.setItem(`recent_searches_users_year_${year}`, JSON.stringify(updated));
                } catch (error) {
                    console.warn('Failed to save recent searches:', error);
                }

                return updated;
            });
        },
        [year],
    );

    // Handle suggestion selection
    const handleSuggestionSelect = useCallback(
        (suggestion: string) => {
            enhancedSearch.setSearchQuery(suggestion);
            handleAddRecentSearch(suggestion);
        },
        [enhancedSearch, handleAddRecentSearch],
    );

    // Reset page when filters or year changes
    useEffect(() => {
        setCurrentPage(1);
    }, [filters, year]);

    // Sync enhanced search with filters on initial mount only (avoid overriding user typing)
    useEffect(() => {
        if (filters.search) {
            enhancedSearch.setSearchQuery(filters.search);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Cache search results when data is received
    useEffect(() => {
        if (users && filters.search && !isLoading && !isError) {
            enhancedSearch.setCachedResult(filters.search, users, pagination?.total || users.length);
        }
    }, [users, filters.search, isLoading, isError, pagination?.total, enhancedSearch]);

    // Update search suggestions with dynamic suggestions from hybrid search
    useEffect(() => {
        if (dynamicSuggestions.length > 0) {
            const newSuggestions = dynamicSuggestions.map((s) => ({
                text: s.text,
                type: 'suggestion' as const,
                count: s.count,
            }));

            setSearchSuggestions((prev) => [
                ...newSuggestions,
                ...prev.filter((s) => s.type !== 'suggestion'), // Keep other types
            ]);
        }
    }, [dynamicSuggestions]);

    // Auto-enable virtual scrolling for large datasets
    useEffect(() => {
        const totalUsers = pagination?.total || users?.length || 0;
        setEnableVirtualScrolling(totalUsers > 100);
    }, [pagination?.total, users?.length]);

    // Prefetch next page when user scrolls or navigates
    useEffect(() => {
        if (pagination && currentPage < pagination.last_page) {
            prefetchNextPage();
        }
    }, [currentPage, pagination, prefetchNextPage]);

    // Enhanced event handlers
    const handleFiltersChange = (newFilters: SearchFilters) => {
        setPersistedFilters(newFilters);
        setCurrentPage(1); // Reset to first page on filter change
    };

    const handleClearAllFilters = () => {
        clearPersistedFilters();
        setCurrentPage(1);
        enhancedSearch.clearSearch();
    };

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };

    const handlePageSizeChange = (size: number) => {
        setPageSize(Math.min(size, 100)); // Respect backend limit
        setCurrentPage(1);
    };

    const handleSort = (column: string, direction: 'asc' | 'desc') => {
        if (['name', 'academic_year', 'created_at', 'birth_date', 'first_attendance_date'].includes(column)) {
            setSortBy(column as typeof sortBy);
            setSortDir(direction);
            setCurrentPage(1);
        }
    };

    const handleBulkMarkAttendance = () => {
        if (selectedUsers.length > 0) {
            bulkMarkAttendance(selectedUsers);
            setSelectedUsers([]);
        }
    };

    // Define functions before they are used in useEffect dependencies
    const handleExport = useCallback(
        (format?: 'csv' | 'excel' | 'pdf') => {
            const exportFormat = format || 'csv';
            const exportFilters = {
                year,
                ...filters,
            };
            exportUsers(exportFormat, exportFilters);
        },
        [year, filters, exportUsers],
    );

    // Keyboard shortcuts
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            // Don't trigger shortcuts when typing in inputs
            if (
                event.target instanceof HTMLInputElement ||
                event.target instanceof HTMLTextAreaElement ||
                event.target instanceof HTMLSelectElement
            ) {
                return;
            }

            const isCtrl = event.ctrlKey || event.metaKey;

            switch (event.key.toLowerCase()) {
                case 'k':
                    if (isCtrl) {
                        event.preventDefault();
                        // Focus search input
                        const searchInput = document.querySelector('input[placeholder*="البحث"]') as HTMLInputElement;
                        if (searchInput) {
                            searchInput.focus();
                        }
                    }
                    break;
                case 'r':
                    if (isCtrl) {
                        event.preventDefault();
                        refetch();
                    }
                    break;
                case 'e':
                    if (isCtrl) {
                        event.preventDefault();
                        handleExport();
                    }
                    break;
                case 'a':
                    if (isCtrl) {
                        event.preventDefault();
                        // Select all visible users
                        setSelectedUsers(users.map((user) => user.id));
                    }
                    break;
                case 'd':
                    if (isCtrl) {
                        event.preventDefault();
                        setSelectedUsers([]);
                    }
                    break;
                case 'p':
                    if (isCtrl) {
                        event.preventDefault();
                        setShowPerformanceMonitor((prev) => !prev);
                    }
                    break;
                case 'escape':
                    event.preventDefault();
                    setShowUserDetails(null);
                    setShowQRModal(null);
                    setShowPerformanceMonitor(false);
                    break;
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [refetch, handleExport, users, setSelectedUsers]);

    // Enhanced debug logging and performance monitoring
    console.log('UsersPage Enhanced Debug:', {
        totalUsers: users.length,
        isLoading,
        isFetching,
        isError,
        error: error?.message,
        year,
        currentPage,
        pageSize,
        filters,
        sortBy,
        sortDir,
        pagination,
        summary,
        filterOptions,
        queryInfo,
        selectedUsersCount: selectedUsers.length,
    });

    // Use hybrid search results when searching, otherwise use server data
    const displayUsers = hybridSearch.searchQuery.length > 0 ? hybridSearch.searchResults.map((result) => result.item) : users;
    // Year names for display
    const yearNames = {
        1: 'السنة الأولى',
        2: 'السنة الثانية',
        3: 'السنة الثالثة',
        4: 'السنة الرابعة',
    };

    // Loading and error states
    const loading = isLoading;
    const errorMessage = isError ? (error as Error)?.message || 'حدث خطأ في تحميل البيانات' : null;

    const columns: ColumnDef<AppUser>[] = [
        {
            id: 'select',
            header: ({ table }) => (
                <Checkbox
                    checked={table.getIsAllPageRowsSelected()}
                    onCheckedChange={(value) => {
                        table.toggleAllPageRowsSelected(!!value);
                        if (value) {
                            setSelectedUsers(table.getRowModel().rows.map((row) => row.original.id));
                        } else {
                            setSelectedUsers([]);
                        }
                    }}
                    aria-label="تحديد الكل"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={selectedUsers.includes(row.original.id)}
                    onCheckedChange={(value) => {
                        if (value) {
                            setSelectedUsers([...selectedUsers, row.original.id]);
                        } else {
                            setSelectedUsers(selectedUsers.filter((id) => id !== row.original.id));
                        }
                    }}
                    aria-label="تحديد الصف"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: 'name',
            header: ({ column }) => <SortableHeader column={column}>الاسم</SortableHeader>,
            cell: ({ row }) => (
                <UserHoverCard user={row.original} isRTL={isRTL}>
                    <div className="flex cursor-pointer items-center gap-3 rounded-md p-1 transition-colors hover:bg-muted/50">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500 shadow-sm">
                            <span className="text-sm font-bold text-white">{row.original.name.charAt(0)}</span>
                        </div>
                        <div className="min-w-0 flex-1">
                            <div className="truncate font-medium">{row.getValue('name')}</div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <Phone className="h-3 w-3" />
                                <span className="truncate">{row.original.phone}</span>
                            </div>
                        </div>
                    </div>
                </UserHoverCard>
            ),
            size: 250,
        },
        {
            accessorKey: 'gender',
            header: 'النوع',
            cell: ({ row }) => {
                const gender = row.getValue('gender') as string;
                const isMale = gender === 'male';
                return (
                    <div className="flex items-center gap-2">
                        <div
                            className={cn(
                                'flex h-6 w-6 items-center justify-center rounded-full text-xs font-medium',
                                isMale
                                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                                    : 'bg-pink-100 text-pink-700 dark:bg-pink-900 dark:text-pink-300',
                            )}
                        >
                            {isMale ? '♂' : '♀'}
                        </div>
                        <Badge
                            variant={isMale ? 'default' : 'secondary'}
                            className={cn(
                                'text-xs',
                                isMale
                                    ? 'bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300'
                                    : 'bg-pink-100 text-pink-800 hover:bg-pink-200 dark:bg-pink-900 dark:text-pink-300',
                            )}
                        >
                            {isMale ? 'ذكر' : 'أنثى'}
                        </Badge>
                    </div>
                );
            },
            size: 100,
        },
        {
            accessorKey: 'college',
            header: 'الكلية والقسم',
            cell: ({ row }) => (
                <div className="flex min-w-0 items-center gap-2">
                    <GraduationCap className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                    <div className="min-w-0 flex-1">
                        <div className="truncate font-medium" title={row.getValue('college')}>
                            {row.getValue('college')}
                        </div>
                        <div className="truncate text-sm text-muted-foreground" title={row.original.department}>
                            {row.original.department}
                        </div>
                    </div>
                </div>
            ),
            size: 200,
        },
        {
            accessorKey: 'birth_date',
            header: 'تاريخ الميلاد',
            cell: ({ row }) => {
                const birthDate = row.getValue('birth_date') as string;
                if (!birthDate) {
                    return (
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <span className="text-sm">غير محدد</span>
                        </div>
                    );
                }

                try {
                    const formattedDate = format(new Date(birthDate), 'dd/MM/yyyy', { locale: ar });
                    const age = new Date().getFullYear() - new Date(birthDate).getFullYear();

                    return (
                        <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <div>
                                <div className="text-sm font-medium">{formattedDate}</div>
                                <div className="text-xs text-muted-foreground">{age} سنة</div>
                            </div>
                        </div>
                    );
                } catch {
                    return (
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <span className="text-sm">تاريخ غير صحيح</span>
                        </div>
                    );
                }
            },
            size: 140,
        },
        {
            accessorKey: 'first_attendance_date',
            header: 'أول حضور',
            cell: ({ row }) => {
                const attendanceDate = row.getValue('first_attendance_date') as string;
                if (!attendanceDate) {
                    return (
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <UserCheck className="h-4 w-4" />
                            <span className="text-sm">لم يحضر بعد</span>
                        </div>
                    );
                }

                try {
                    const formattedDate = format(new Date(attendanceDate), 'dd/MM/yyyy', { locale: ar });
                    const daysAgo = Math.floor((Date.now() - new Date(attendanceDate).getTime()) / (1000 * 60 * 60 * 24));

                    return (
                        <div className="flex items-center gap-2">
                            <UserCheck className="h-4 w-4 text-green-600" />
                            <div>
                                <div className="text-sm font-medium">{formattedDate}</div>
                                <div className="text-xs text-muted-foreground">
                                    {daysAgo === 0 ? 'اليوم' : daysAgo === 1 ? 'أمس' : `منذ ${daysAgo} يوم`}
                                </div>
                            </div>
                        </div>
                    );
                } catch {
                    return (
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <UserCheck className="h-4 w-4" />
                            <span className="text-sm">تاريخ غير صحيح</span>
                        </div>
                    );
                }
            },
            size: 140,
        },
        {
            id: 'actions',
            header: 'الإجراءات',
            cell: ({ row }) => {
                const user = row.original;

                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => setShowUserDetails(user)}>
                                <Eye className="mr-2 h-4 w-4" />
                                عرض التفاصيل
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setShowQRModal(user)}>
                                <QrCode className="mr-2 h-4 w-4" />
                                عرض QR Code
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setSelectedUser(user)}>
                                <Edit className="mr-2 h-4 w-4" />
                                تعديل
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => bulkMarkAttendance([user.id], true, '<EMAIL>')} className="text-green-600">
                                <UserCheck className="mr-2 h-4 w-4" />
                                تسجيل حضور
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => deleteUser(user.id)} className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                حذف
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];

    // Create table instance for virtual scrolling (after columns declaration)
    const table = useReactTable({
        data: displayUsers || [],
        columns,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        manualPagination: true,
        pageCount: pagination?.last_page || 1,
        state: {
            pagination: {
                pageIndex: currentPage - 1,
                pageSize,
            },
        },
    });

    // TODO: Implement server-side filters later
    // const filtersComponent = null;

    const handleBulkAction = (action: string) => {
        if (action === 'mark_present') {
            bulkMarkAttendance(selectedUsers, true, '<EMAIL>');
        } else if (action === 'mark_absent') {
            bulkMarkAttendance(selectedUsers, false, '<EMAIL>');
        }
        setSelectedUsers([]);
    };

    // Debug: Log component state
    console.log('UsersPage Render Debug:', {
        isLoading,
        isFetching,
        isError,
        error: error?.message,
        usersCount: users?.length,
        pagination,
        year,
        searchQuery: filters.search,
        filters,
        currentPage,
    });

    // Early return for debugging
    if (isError) {
        return (
            <div className="p-6">
                <h1>Error Loading Users</h1>
                <p>Error: {error?.message || 'Unknown error'}</p>
                <button onClick={() => refetch()}>Retry</button>
            </div>
        );
    }

    return (
        <div className="page-transition space-y-6 p-6 font-cairo" dir={direction}>
            {/* Breadcrumb Navigation */}
            <nav className="flex" aria-label="Breadcrumb">
                <ol className={cn('inline-flex items-center space-x-1 md:space-x-3', isRTL && 'flex-row-reverse space-x-reverse')}>
                    <li className="inline-flex items-center">
                        <a
                            href="/"
                            className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"
                        >
                            <svg
                                className="mr-2.5 h-3 w-3"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                            >
                                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            الرئيسية
                        </a>
                    </li>
                    <li>
                        <div className="flex items-center">
                            <svg
                                className="mx-1 h-3 w-3 text-gray-400"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 6 10"
                            >
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <a
                                href="/users"
                                className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white"
                            >
                                إدارة الطلاب
                            </a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div className="flex items-center">
                            <svg
                                className="mx-1 h-3 w-3 text-gray-400"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 6 10"
                            >
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{yearNames[year]}</span>
                        </div>
                    </li>
                </ol>
            </nav>

            {/* Enhanced Header with Statistics */}
            <div className="animate-fade-in space-y-6">
                {/* Main Header */}
                <div className={cn('flex items-center justify-between', 'flex-row')}>
                    <div className="space-y-2">
                        <div className={cn('flex items-center gap-4', 'flex-row')}>
                            <div className="rounded-xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 p-3 shadow-lg">
                                <Users className="h-8 w-8 text-white" />
                            </div>
                            <div>
                                <h1 className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-4xl font-bold text-transparent">
                                    {yearNames[year]}
                                </h1>
                                <p className="text-lg text-muted-foreground">إدارة طلاب {yearNames[year]} - عرض وتعديل بيانات الطلاب</p>
                            </div>
                        </div>
                    </div>

                    <div className="flex gap-2">
                        {/* Keyboard Shortcuts Indicator */}
                        <div className="hidden items-center gap-2 rounded-lg bg-muted/50 px-3 py-2 text-xs text-muted-foreground md:flex">
                            <Keyboard className="h-3 w-3" />
                            <span>اضغط</span>
                            <kbd className="rounded-lg border border-gray-200 bg-gray-100 px-1.5 py-0.5 text-xs font-semibold text-gray-800 dark:border-gray-500 dark:bg-gray-600 dark:text-gray-100">
                                ?
                            </kbd>
                            <span>للاختصارات</span>
                        </div>

                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline">
                                    <Download className="mr-2 h-4 w-4" />
                                    تصدير
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                                <DropdownMenuItem onClick={() => handleExport('csv')}>تصدير CSV</DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleExport('excel')}>تصدير Excel</DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleExport('pdf')}>تصدير PDF</DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>

                        <Button variant="outline">
                            <Upload className="mr-2 h-4 w-4" />
                            استيراد
                        </Button>

                        <Button className="btn-gradient">
                            <Plus className="mr-2 h-4 w-4" />
                            إضافة طالب جديد
                        </Button>
                    </div>
                </div>
            </div>

            {/* Enhanced Statistics Cards */}
            {summary && !loading && (
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {/* Total Users */}
                    <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
                        <CardContent className="p-6">
                            <div className="flex items-center gap-4">
                                <div className="rounded-lg bg-blue-500 p-3">
                                    <Users className="h-6 w-6 text-white" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-blue-600 dark:text-blue-400">إجمالي الطلاب</p>
                                    <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{summary.total_users.toLocaleString('ar')}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Active Users */}
                    <Card className="border-0 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
                        <CardContent className="p-6">
                            <div className="flex items-center gap-4">
                                <div className="rounded-lg bg-green-500 p-3">
                                    <UserCheck className="h-6 w-6 text-white" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-green-600 dark:text-green-400">الطلاب النشطون</p>
                                    <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                                        {summary.active_users.toLocaleString('ar')}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Current Year */}
                    <Card className="border-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
                        <CardContent className="p-6">
                            <div className="flex items-center gap-4">
                                <div className="rounded-lg bg-purple-500 p-3">
                                    <GraduationCap className="h-6 w-6 text-white" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-purple-600 dark:text-purple-400">{yearNames[year]}</p>
                                    <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                                        {(summary.filtered_count || users.length).toLocaleString('ar')}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* QR Enabled */}
                    <Card className="border-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
                        <CardContent className="p-6">
                            <div className="flex items-center gap-4">
                                <div className="rounded-lg bg-orange-500 p-3">
                                    <QrCode className="h-6 w-6 text-white" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-orange-600 dark:text-orange-400">لديهم QR</p>
                                    <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                                        {summary.users_with_qr.toLocaleString('ar')}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Error State */}
            {errorMessage && (
                <Card className="border-red-200 bg-red-50">
                    <CardContent className="p-4 text-red-700">{errorMessage}</CardContent>
                </Card>
            )}

            {/* Enhanced Loading State with Skeleton */}
            {loading && (
                <div className="space-y-6">
                    {/* Search Skeleton */}
                    <div className="space-y-4">
                        <div className="h-11 w-full animate-pulse rounded-md bg-muted" />
                        <div className="flex gap-2">
                            <div className="h-9 w-20 animate-pulse rounded-md bg-muted" />
                            <div className="h-9 w-16 animate-pulse rounded-md bg-muted" />
                            <div className="h-9 w-24 animate-pulse rounded-md bg-muted" />
                        </div>
                    </div>

                    {/* Table Skeleton */}
                    <div className="rounded-md border">
                        <div className="border-b p-4">
                            <div className="flex gap-4">
                                <div className="h-4 w-4 animate-pulse rounded bg-muted" />
                                <div className="h-4 w-20 animate-pulse rounded bg-muted" />
                                <div className="h-4 w-24 animate-pulse rounded bg-muted" />
                                <div className="h-4 w-16 animate-pulse rounded bg-muted" />
                                <div className="h-4 w-20 animate-pulse rounded bg-muted" />
                                <div className="h-4 w-16 animate-pulse rounded bg-muted" />
                                <div className="h-4 w-16 animate-pulse rounded bg-muted" />
                            </div>
                        </div>
                        <div className="divide-y">
                            {Array.from({ length: 10 }).map((_, index) => (
                                <div key={index} className="p-4">
                                    <div className="flex items-center gap-4">
                                        <div className="h-4 w-4 animate-pulse rounded bg-muted" />
                                        <div className="flex items-center gap-3">
                                            <div className="h-10 w-10 animate-pulse rounded-full bg-muted" />
                                            <div className="space-y-1">
                                                <div className="h-4 w-32 animate-pulse rounded bg-muted" />
                                                <div className="h-3 w-24 animate-pulse rounded bg-muted" />
                                            </div>
                                        </div>
                                        <div className="h-6 w-16 animate-pulse rounded-full bg-muted" />
                                        <div className="h-4 w-12 animate-pulse rounded bg-muted" />
                                        <div className="h-4 w-20 animate-pulse rounded bg-muted" />
                                        <div className="h-4 w-20 animate-pulse rounded bg-muted" />
                                        <div className="flex gap-2">
                                            <div className="h-8 w-8 animate-pulse rounded bg-muted" />
                                            <div className="h-8 w-8 animate-pulse rounded bg-muted" />
                                            <div className="h-8 w-8 animate-pulse rounded bg-muted" />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Fetching Indicator */}
            {isFetching && !loading && (
                <div className="fixed top-4 right-4 z-50">
                    <Card className="border-blue-200 bg-blue-50">
                        <CardContent className="p-2 text-sm text-blue-700">
                            <div className="flex items-center gap-2">
                                <div className="h-3 w-3 animate-spin rounded-full border border-blue-600 border-t-transparent"></div>
                                تحديث البيانات...
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Bulk Actions */}
            {selectedUsers.length > 0 && (
                <Card className="animate-scale-in border-blue-200 bg-blue-50">
                    <CardContent className="flex items-center justify-between p-4">
                        <div className="flex items-center gap-3">
                            <CheckCircle className="h-5 w-5 text-blue-600" />
                            <span className="font-medium text-blue-800">تم تحديد {selectedUsers.length} طالب</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button size="sm" onClick={() => handleBulkAction('mark_present')} className="bg-green-600 hover:bg-green-700">
                                <UserCheck className="mr-2 h-4 w-4" />
                                تسجيل حضور
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleBulkAction('mark_absent')}>
                                تسجيل غياب
                            </Button>
                            <Button size="sm" variant="ghost" onClick={() => setSelectedUsers([])}>
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Enhanced Data Table with Advanced Search and Filtering */}
            {!loading && !errorMessage && (
                <div className="space-y-4">
                    {/* Enhanced Search and Filters */}
                    <div className="space-y-4">
                        {/* Enhanced Search Input with Suggestions and Caching */}
                        <EnhancedSearchInput
                            value={hybridSearch.searchQuery}
                            onChange={hybridSearch.setSearchQuery}
                            onClear={hybridSearch.clearSearch}
                            placeholder={`البحث في الطلاب... (${hybridSearch.searchMode === 'client' ? 'بحث محلي سريع' : 'بحث خادم'})`}
                            isSearching={hybridSearch.isSearching || isFetching}
                            searchResults={hybridSearch.searchResults.map((r) => r.item) || []}
                            isRTL={isRTL}
                            showSearchStats={true}
                            cacheHit={hybridSearch.searchMode === 'client'}
                            totalResults={hybridSearch.totalResults || pagination?.total}
                            searchTime={queryInfo?.execution_time}
                            suggestions={searchSuggestions}
                            onSuggestionSelect={handleSuggestionSelect}
                            showSuggestions={true}
                            recentSearches={recentSearches}
                        />

                        {/* Advanced Filter System */}
                        <AdvancedFilters
                            filters={filters}
                            filterOptions={{
                                departments: filterOptions?.departments?.map((dept: string) => ({
                                    value: dept,
                                    label: dept,
                                })),
                                colleges: filterOptions?.colleges?.map((college: string) => ({
                                    value: college,
                                    label: college,
                                })),
                            }}
                            onFiltersChange={handleFiltersChange}
                            onClearAll={handleClearAllFilters}
                            isRTL={isRTL}
                            showAdvanced={false}
                        />
                    </div>

                    {/* Enhanced Data Table with Virtual Scrolling */}
                    {enableVirtualScrolling ? (
                        <div className="space-y-4">
                            <VirtualTable
                                table={table}
                                data={displayUsers || []}
                                enableVirtualization={true}
                                estimateSize={80}
                                overscan={5}
                                isLoading={isLoading}
                                loadingRows={10}
                                onScroll={(scrollTop: number) => {
                                    // Handle scroll events for infinite loading if needed
                                    console.log('Virtual table scroll:', scrollTop);
                                }}
                            />
                            <div className="mt-4">
                                <EnhancedPagination
                                    pagination={{
                                        pageIndex: currentPage - 1,
                                        pageSize,
                                        pageCount: pagination?.last_page || 1,
                                        total: pagination?.total || 0,
                                    }}
                                    onPageChange={(page) => handlePageChange(page)}
                                    onPageSizeChange={(size) => handlePageSizeChange(size)}
                                    isRTL={isRTL}
                                />
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <DataTable
                                columns={columns}
                                data={displayUsers}
                                enablePagination={false}
                                enableGlobalFilter={false}
                                // Sorting handled by column headers; server-side apply in handlers
                                enableSorting={true}
                                toolbar={
                                    <div className="flex items-center gap-2">
                                        {selectedUsers.length > 0 && (
                                            <div className="flex items-center gap-2">
                                                <Badge variant="secondary">{selectedUsers.length} محدد</Badge>
                                                <Button size="sm" onClick={() => handleBulkAction('mark_present')}>
                                                    تسجيل حضور
                                                </Button>
                                                <Button size="sm" variant="outline" onClick={() => handleBulkAction('mark_absent')}>
                                                    تسجيل غياب
                                                </Button>
                                            </div>
                                        )}
                                        <Button variant="outline" size="sm" onClick={() => handleExport()}>
                                            <Download className="mr-2 h-4 w-4" />
                                            تصدير
                                        </Button>
                                    </div>
                                }
                                isRTL={isRTL}
                            />
                            <div className="mt-4">
                                <EnhancedPagination
                                    pagination={{
                                        pageIndex: currentPage - 1,
                                        pageSize,
                                        pageCount: pagination?.last_page || 1,
                                        total: pagination?.total || 0,
                                    }}
                                    onPageChange={(page) => handlePageChange(page)}
                                    onPageSizeChange={(size) => handlePageSizeChange(size)}
                                    isRTL={isRTL}
                                />
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* No Data State */}
            {!loading && !errorMessage && displayUsers.length === 0 && (
                <Card>
                    <CardContent className="p-6 text-center">
                        <Users className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                        <h3 className="mb-2 text-lg font-medium text-gray-900">لا توجد بيانات</h3>
                        <p className="text-gray-500">لم يتم العثور على طلاب في {yearNames[year]}</p>
                    </CardContent>
                </Card>
            )}

            {/* Enhanced User Details Modal */}
            {showUserDetails && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm">
                    <Card className="animate-scale-in max-h-[90vh] w-full max-w-4xl overflow-y-auto border-0 shadow-2xl">
                        <CardHeader className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white">
                            <div className="flex items-center justify-between">
                                <CardTitle className="flex items-center gap-3 text-xl">
                                    <div className="rounded-lg bg-white/20 p-2">
                                        <Eye className="h-6 w-6" />
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-bold">{showUserDetails.name}</h3>
                                        <p className="text-sm text-white/80">تفاصيل الطالب</p>
                                    </div>
                                </CardTitle>
                                <Button variant="ghost" size="icon" onClick={() => setShowUserDetails(null)} className="text-white hover:bg-white/20">
                                    <X className="h-5 w-5" />
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div className="space-y-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">الاسم الكامل</label>
                                        <p className="text-lg font-medium">{showUserDetails.name}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">رقم الهاتف</label>
                                        <p className="flex items-center gap-2">
                                            <Phone className="h-4 w-4" />
                                            {showUserDetails.phone}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">النوع</label>
                                        <p>
                                            <Badge variant={showUserDetails.gender === 'male' ? 'default' : 'secondary'}>
                                                {showUserDetails.gender === 'male' ? 'ذكر' : 'أنثى'}
                                            </Badge>
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">السنة الدراسية</label>
                                        <p className="text-lg font-medium">السنة {showUserDetails.year}</p>
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">الكلية</label>
                                        <p className="flex items-center gap-2">
                                            <GraduationCap className="h-4 w-4" />
                                            {showUserDetails.college}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">القسم</label>
                                        <p>{showUserDetails.department}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">تاريخ الميلاد</label>
                                        <p className="flex items-center gap-2">
                                            <Calendar className="h-4 w-4" />
                                            {format(new Date(showUserDetails.birthdate), 'dd MMMM yyyy', { locale: ar })}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">أول حضور</label>
                                        <p>{format(new Date(showUserDetails.first_attendance_date), 'dd MMMM yyyy', { locale: ar })}</p>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-500">العنوان</label>
                                <p className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4" />
                                    {showUserDetails.address}
                                </p>
                            </div>

                            {showUserDetails.facebook_url && (
                                <div>
                                    <label className="text-sm font-medium text-gray-500">الفيسبوك</label>
                                    <p className="flex items-center gap-2">
                                        <Facebook className="h-4 w-4" />
                                        <a
                                            href={showUserDetails.facebook_url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:underline"
                                        >
                                            {showUserDetails.facebook_url}
                                        </a>
                                    </p>
                                </div>
                            )}

                            <div className="flex gap-2 border-t pt-4">
                                <Button onClick={() => setShowQRModal(showUserDetails)} variant="outline" className="flex-1">
                                    <QrCode className="mr-2 h-4 w-4" />
                                    عرض QR Code
                                </Button>
                                <Button onClick={() => setSelectedUser(showUserDetails)} className="btn-gradient flex-1">
                                    <Edit className="mr-2 h-4 w-4" />
                                    تعديل البيانات
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Enhanced QR Code Modal */}
            {showQRModal && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                    <Card className="animate-scale-in w-full max-w-md">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle className="flex items-center gap-2">
                                    <QrCode className="h-5 w-5" />
                                    QR Code - {showQRModal.name}
                                </CardTitle>
                                <Button variant="ghost" size="icon" onClick={() => setShowQRModal(null)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4 text-center">
                            <div className="mx-auto flex h-48 w-48 items-center justify-center rounded-lg bg-gray-100">
                                <QrCode className="h-24 w-24 text-gray-400" />
                            </div>
                            <div>
                                <p className="font-medium">{showQRModal.name}</p>
                                <p className="text-sm text-gray-500">
                                    السنة {showQRModal.year} - {showQRModal.phone}
                                </p>
                            </div>
                            <p className="text-sm text-gray-600">امسح هذا الرمز لتسجيل الحضور</p>
                            <div className="flex gap-2">
                                <Button variant="outline" className="flex-1 bg-transparent">
                                    <Download className="mr-2 h-4 w-4" />
                                    تحميل
                                </Button>
                                <Button variant="outline" className="flex-1 bg-transparent">
                                    طباعة
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Search Performance Monitor */}
            <SearchPerformanceMonitor
                metrics={searchMetrics}
                currentSearchMode={hybridSearch.searchMode}
                isVisible={showPerformanceMonitor}
                onClose={() => setShowPerformanceMonitor(false)}
            />
        </div>
    );
}

UsersPage.layout = (page: React.ReactElement) => <AppLayout children={page} />;

export default UsersPage;
