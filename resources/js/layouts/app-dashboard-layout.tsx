import { useRTL } from '@/contexts/rtl-context';
import { cn } from '@/lib/utils';
import { useAppStore } from '@/stores/app-store';
import React, { useEffect } from 'react';

interface AppDashboardLayoutProps {
    children: React.ReactNode;
}

export function AppDashboardLayout({ children }: AppDashboardLayoutProps) {
    const { direction } = useRTL();
    const { clearAndReinitialize, initialized, users } = useAppStore();

    // Initialize mock data on mount - check both initialized flag and actual data
    useEffect(() => {
        if (!initialized || users.length === 0) {
            console.log('Initializing mock data...', { initialized, usersCount: users.length });
            clearAndReinitialize();
        }
    }, [initialized, users.length, clearAndReinitialize]);

    // Add debug function to window for manual testing
    useEffect(() => {
        if (typeof window !== 'undefined') {
            (window as any).debugStore = {
                clearAndReinitialize,
                getStoreState: () => ({ initialized, usersCount: users.length }),
            };
        }
    }, [clearAndReinitialize, initialized, users.length]);

    return (
        <div dir={direction} className={cn('min-h-screen bg-gray-50', direction)}>
            <div className="font-cairo">{children}</div>
        </div>
    );
}
