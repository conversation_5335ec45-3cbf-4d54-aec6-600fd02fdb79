import { addDays, format, subDays } from 'date-fns';

// Enhanced Christian names for comprehensive mock data
const christian<PERSON>ames = {
    male: [
        'مايكل جورج صبحي',
        'مينا صموئيل فهيم',
        'كيرلس أنطونيوس نصيف',
        'مارك بولس عبدالله',
        'أندرو يوسف حنا',
        'جون مرقس إبراهيم',
        'بيتر إبراهيم يوسف',
        'ديفيد متى جرجس',
        'دانيال لوقا عزيز',
        'صموئيل يعقوب ميخائيل',
        'إسحق موسى فايز',
        'يوسف هارون نبيل',
        'متى فيليب سمير',
        'لوقا توما عادل',
        'مرقس برثولماوس رامي',
        'أنطونيوس سمعان وديع',
        'جرجس اسطفانوس مجدي',
        'كيرلس باسيليوس عماد',
        'أثناسيوس غريغوريوس شريف',
        'يوحنا خريستوستوم كريم',
        'مكاريوس أنبا بولا فادي',
        'شنودة الأرشيدياكون طارق',
        'بيشوي أنبا أنطونيوس هاني',
        'صرابامون أنبا بيشوي ماهر',
        'موسى الأسود عصام',
        'باخوميوس أنبا شنودة أيمن',
        'إفرام السرياني جمال',
        'يوأنس كاسيان خالد',
        'مكسيموس المعترف وائل',
        'يوحنا السلمي باسم',
        'أنطونيوس الكبير فؤاد',
        'بولا الطيبي رفيق',
        'مقار الكبير سامح',
        'أرسانيوس معلم أولاد الملوك علاء',
        'بيمن الكبير محمد',
        'سيصوي الكبير أحمد',
        'نيلوس السينائي حسام',
        'يوحنا القصير إيهاب',
        'أمونيوس الأسقف مصطفى',
        'بفنوتيوس الراهب ياسر',
        'أغابيوس الشهيد كريم',
        'أباكير ويوحنا عماد',
        'أبانوب النهيسي شريف',
        'أبرآم الفيومي وائل',
        'أبسخيرون القليني باسم',
        'أثناسيوس الرسولي فؤاد',
        'أرميا النبي رفيق',
        'أنبا رويس سامح',
        'أنبا موسى الأسود علاء',
        'أنبا بيشوي محمد',
    ],
    female: [
        'مريم يوسف عبدالله',
        'مارثا لعازر جرجس',
        'مريم المجدلية حنا',
        'سوسنة دانيال فهيم',
        'راعوث نعمي صبحي',
        'أستير مردخاي نصيف',
        'يهوديت هولوفرنيس عزيز',
        'حنة صموئيل ميخائيل',
        'أليصابات زكريا فايز',
        'سارة إبراهيم نبيل',
        'رفقة إسحق سمير',
        'راحيل يعقوب عادل',
        'ليئة يعقوب رامي',
        'دينة يعقوب وديع',
        'تامار يهوذا مجدي',
        'ميريام موسى عماد',
        'دبورة باراق شريف',
        'يائيل سيسرا كريم',
        'راعوث بوعز فادي',
        'حنة ألقانة طارق',
        'أبيجايل داود هاني',
        'بثشبع داود ماهر',
        'الملكة أستير عصام',
        'يهوديت بيت فلوى أيمن',
        'سوسنة البابلية جمال',
        'القديسة مريم العذراء خالد',
        'القديسة فيرينا وائل',
        'القديسة كاترين باسم',
        'القديسة بربارة فؤاد',
        'القديسة مارينا رفيق',
        'القديسة دميانة سامح',
        'القديسة رفقة علاء',
        'القديسة يوليانة محمد',
        'القديسة أوجينيا أحمد',
        'القديسة تكلا حسام',
        'القديسة سوسنة إيهاب',
        'القديسة أنسطاسيا مصطفى',
        'القديسة أغاثا ياسر',
        'القديسة لوسيا منير',
        'القديسة سيسيليا عبير',
        'القديسة فيلومينا نادية',
        'القديسة أجاثا سمر',
        'القديسة إيريني هالة',
        'القديسة أولمبياس فاطمة',
        'القديسة بائيسة نهى',
        'القديسة تاييس رانيا',
        'القديسة جوليتا ليلى',
        'القديسة دولاجي سهير',
        'القديسة رفقة منى',
        'القديسة سارة إيمان',
    ],
};

// Enhanced colleges with more variety for comprehensive testing
const colleges = [
    'كلية الطب',
    'كلية الهندسة',
    'كلية الصيدلة',
    'كلية طب الأسنان',
    'كلية العلوم',
    'كلية التجارة',
    'كلية الحقوق',
    'كلية الآداب',
    'كلية التربية',
    'كلية الزراعة',
    'كلية الطب البيطري',
    'كلية الحاسبات والمعلومات',
    'كلية الفنون الجميلة',
    'كلية التربية الرياضية',
    'كلية الإعلام',
    'كلية السياحة والفنادق',
    'كلية التمريض',
    'كلية العلاج الطبيعي',
    'كلية الألسن',
    'كلية الاقتصاد والعلوم السياسية',
    'كلية الموسيقى',
    'كلية الفنون التطبيقية',
    'كلية التكنولوجيا الحيوية',
    'كلية الدراسات الإسلامية',
    'كلية الخدمة الاجتماعية',
    'كلية رياض الأطفال',
    'كلية التربية النوعية',
    'كلية الآثار',
    'كلية الجغرافيا ونظم المعلومات',
    'كلية الأرصاد الجوية',
];

// Enhanced departments with comprehensive coverage for testing
const departments = {
    'كلية الطب': ['طب عام', 'جراحة', 'باطنة', 'أطفال', 'نساء وتوليد', 'عيون', 'أنف وأذن', 'جلدية', 'نفسية', 'أشعة', 'تخدير', 'مسالك بولية'],
    'كلية الهندسة': ['مدني', 'معماري', 'ميكانيكا', 'كهرباء', 'حاسبات', 'بترول', 'كيميائية', 'طيران', 'بحري', 'نووي', 'بيئي', 'صناعي'],
    'كلية الصيدلة': ['صيدلة إكلينيكية', 'كيمياء صيدلية', 'عقاقير', 'صيدلانيات', 'علم الأدوية', 'ميكروبيولوجيا', 'كيمياء حيوية'],
    'كلية طب الأسنان': ['جراحة الفم', 'تقويم الأسنان', 'طب أسنان الأطفال', 'استعاضة', 'علاج الجذور', 'أمراض اللثة', 'طب الفم'],
    'كلية العلوم': ['رياضيات', 'فيزياء', 'كيمياء', 'أحياء', 'جيولوجيا', 'حاسب آلي', 'إحصاء', 'فلك', 'بيئة', 'كيمياء حيوية'],
    'كلية التجارة': ['محاسبة', 'إدارة أعمال', 'اقتصاد', 'إحصاء', 'نظم معلومات', 'تأمين', 'تسويق', 'إدارة مالية', 'تجارة دولية'],
    'كلية الحقوق': ['قانون عام', 'قانون خاص', 'قانون دولي', 'شريعة إسلامية', 'قانون جنائي', 'قانون تجاري', 'قانون مدني', 'قانون إداري'],
    'كلية الآداب': ['لغة عربية', 'لغة إنجليزية', 'تاريخ', 'جغرافيا', 'فلسفة', 'علم نفس', 'اجتماع', 'آثار', 'مكتبات', 'ترجمة'],
    'كلية التربية': ['تربية ابتدائي', 'تربية إعدادي', 'تربية ثانوي', 'تربية خاصة', 'رياض أطفال', 'تكنولوجيا تعليم', 'مناهج وطرق تدريس'],
    'كلية الزراعة': ['إنتاج نباتي', 'إنتاج حيواني', 'علوم الأراضي', 'اقتصاد زراعي', 'هندسة زراعية', 'وقاية نبات', 'بساتين', 'محاصيل'],
    'كلية الطب البيطري': ['طب باطني', 'جراحة', 'أمراض معدية', 'صحة عامة', 'تشريح', 'فسيولوجيا', 'باثولوجيا', 'طفيليات'],
    'كلية الحاسبات والمعلومات': ['علوم حاسب', 'نظم معلومات', 'تكنولوجيا معلومات', 'ذكاء اصطناعي', 'هندسة برمجيات', 'أمن معلومات', 'شبكات'],
    'كلية الفنون الجميلة': ['رسم وتصوير', 'نحت', 'جرافيك', 'ديكور', 'خزف', 'طباعة', 'تصميم', 'فنون شعبية'],
    'كلية التربية الرياضية': ['تدريب رياضي', 'إدارة رياضية', 'ترويح', 'تأهيل حركي', 'علوم حركة', 'طب رياضي'],
    'كلية الإعلام': ['صحافة', 'إذاعة وتلفزيون', 'علاقات عامة', 'إعلان', 'إعلام رقمي', 'سينما', 'مسرح'],
    'كلية السياحة والفنادق': ['سياحة', 'فنادق', 'إرشاد سياحي', 'طيران مدني', 'إدارة سياحية'],
    'كلية التمريض': ['تمريض عام', 'تمريض باطني', 'تمريض جراحي', 'تمريض أطفال', 'تمريض نفسي', 'تمريض مجتمعي'],
    'كلية العلاج الطبيعي': ['علاج طبيعي عام', 'علاج طبيعي للأطفال', 'علاج طبيعي رياضي', 'علاج طبيعي للمسنين'],
    'كلية الألسن': ['لغة إنجليزية', 'لغة فرنسية', 'لغة ألمانية', 'لغة إيطالية', 'لغة صينية', 'لغة يابانية', 'لغة روسية', 'لغة إسبانية'],
    'كلية الاقتصاد والعلوم السياسية': ['اقتصاد', 'علوم سياسية', 'إحصاء', 'دبلوماسية', 'إدارة عامة', 'علاقات دولية'],
    'كلية الموسيقى': ['آلات موسيقية', 'غناء', 'تأليف موسيقي', 'قيادة أوركسترا'],
    'كلية الفنون التطبيقية': ['تصميم جرافيك', 'تصميم صناعي', 'تصميم داخلي', 'تصميم أزياء'],
    'كلية التكنولوجيا الحيوية': ['هندسة وراثية', 'تكنولوجيا حيوية', 'بيولوجيا جزيئية'],
    'كلية الدراسات الإسلامية': ['فقه', 'تفسير', 'حديث', 'عقيدة'],
    'كلية الخدمة الاجتماعية': ['خدمة اجتماعية', 'تنمية مجتمعية', 'رعاية اجتماعية'],
    'كلية رياض الأطفال': ['تربية طفل', 'علم نفس طفل', 'مناهج رياض أطفال'],
    'كلية التربية النوعية': ['تكنولوجيا تعليم', 'إعلام تربوي', 'اقتصاد منزلي'],
    'كلية الآثار': ['آثار مصرية', 'آثار إسلامية', 'آثار يونانية رومانية'],
    'كلية الجغرافيا ونظم المعلومات': ['جغرافيا طبيعية', 'جغرافيا بشرية', 'نظم معلومات جغرافية'],
    'كلية الأرصاد الجوية': ['أرصاد جوية', 'علوم مناخ', 'فيزياء جوية'],
};

// Enhanced addresses with more variety and detail for comprehensive testing
const addresses = [
    'شارع الجمهورية، وسط البلد، القاهرة',
    'شارع فيصل، الهرم، الجيزة',
    'مدينة نصر، القاهرة الجديدة',
    'المعادي، القاهرة',
    'الزمالك، القاهرة',
    'مصر الجديدة، هليوبوليس',
    'شبرا الخيمة، القليوبية',
    'الإسكندرية، كورنيش النيل',
    'طنطا، الغربية',
    'المنصورة، الدقهلية',
    'أسيوط، صعيد مصر',
    'سوهاج، صعيد مصر',
    'قنا، صعيد مصر',
    'الأقصر، صعيد مصر',
    'أسوان، جنوب مصر',
    'بورسعيد، قناة السويس',
    'الإسماعيلية، قناة السويس',
    'السويس، البحر الأحمر',
    'الغردقة، البحر الأحمر',
    'شرم الشيخ، جنوب سيناء',
    'العريش، شمال سيناء',
    'دمياط، دلتا النيل',
    'كفر الشيخ، دلتا النيل',
    'بنها، القليوبية',
    'الزقازيق، الشرقية',
    'بني سويف، صعيد مصر',
    'الفيوم، صعيد مصر',
    'المنيا، صعيد مصر',
    'ملوي، المنيا',
    'أبو تيج، أسيوط',
    'مدينة السادات، المنوفية',
    'العبور، القليوبية',
    'الشيخ زايد، الجيزة',
    'أكتوبر، الجيزة',
    'التجمع الخامس، القاهرة الجديدة',
    'الرحاب، القاهرة الجديدة',
    'المقطم، القاهرة',
    'حدائق الأهرام، الجيزة',
    'فيصل، الجيزة',
    'إمبابة، الجيزة',
    'بولاق الدكرور، الجيزة',
    'العجوزة، الجيزة',
    'المهندسين، الجيزة',
    'الدقي، الجيزة',
    'حلوان، القاهرة',
    'المطرية، القاهرة',
    'عين شمس، القاهرة',
    'مدينة بدر، القاهرة',
    'العاشر من رمضان، الشرقية',
    'برج العرب، الإسكندرية',
];

// Additional data for comprehensive testing scenarios
const attendancePatterns = {
    excellent: { min: 0.9, max: 1.0 }, // 90-100% attendance
    good: { min: 0.75, max: 0.89 }, // 75-89% attendance
    average: { min: 0.6, max: 0.74 }, // 60-74% attendance
    poor: { min: 0.4, max: 0.59 }, // 40-59% attendance
    critical: { min: 0.0, max: 0.39 }, // 0-39% attendance
};

const userPersonalities = [
    'منتظم جداً',
    'منتظم',
    'متوسط الانتظام',
    'غير منتظم',
    'متقطع الحضور',
    'جديد في النظام',
    'عضو مؤسس',
    'قيادي',
    'نشط اجتماعياً',
    'هادئ ومنطوي',
];

const specialEvents = [
    'مؤتمر الشباب السنوي',
    'رحلة روحية',
    'خدمة اجتماعية',
    'احتفال بعيد الميلاد',
    'ورشة تدريبية',
    'لقاء مع الآباء',
    'صلاة خاصة',
    'نشاط رياضي',
    'مسابقة ثقافية',
    'حفل تخرج',
];

// Enhanced user generation with more realistic patterns and comprehensive data
export function generateMockUsers(count = 200) {
    const users: any[] = [];

    for (let i = 0; i < count; i++) {
        const gender = Math.random() > 0.5 ? 'male' : 'female';
        const names = christianNames[gender];
        const name = names[Math.floor(Math.random() * names.length)];
        const college = colleges[Math.floor(Math.random() * colleges.length)];
        const departmentList = (departments as any)[college] || ['قسم عام'];
        const department = departmentList[Math.floor(Math.random() * departmentList.length)];

        // Generate realistic phone number with Egyptian format
        const phonePrefix = ['010', '011', '012', '015'][Math.floor(Math.random() * 4)];
        const phoneNumber =
            phonePrefix +
            Math.floor(Math.random() * 100000000)
                .toString()
                .padStart(8, '0');

        // Generate birthdate with better distribution (16-30 years old)
        // Ensure birthdays are distributed throughout the year for birthday page testing
        const age = 16 + Math.floor(Math.random() * 15);
        const birthYear = new Date().getFullYear() - age;
        const birthMonth = Math.floor(Math.random() * 12);
        const birthDay = Math.floor(Math.random() * 28) + 1;
        const birthdate = new Date(birthYear, birthMonth, birthDay);

        // Generate first attendance date with more realistic patterns
        const daysAgo = Math.floor(Math.random() * 1095); // Up to 3 years ago
        const firstAttendanceDate = subDays(new Date(), daysAgo);

        // Assign attendance personality for realistic patterns
        const personalityIndex = Math.floor(Math.random() * userPersonalities.length);
        const personality = userPersonalities[personalityIndex];

        // Create more realistic Facebook URLs
        const cleanName = name.replace(/\s+/g, '').replace(/[^\w]/g, '').toLowerCase();
        const facebookUrl = Math.random() > 0.4 ? `https://facebook.com/${cleanName}${Math.floor(Math.random() * 999)}` : '';

        const user = {
            id: `user_${i + 1}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            name,
            phone: phoneNumber,
            gender: gender as 'male' | 'female',
            year: (Math.floor(Math.random() * 4) + 1) as 1 | 2 | 3 | 4,
            college,
            department,
            birthdate: format(birthdate, 'yyyy-MM-dd'),
            address: addresses[Math.floor(Math.random() * addresses.length)],
            facebook_url: facebookUrl,
            first_attendance_date: format(firstAttendanceDate, 'yyyy-MM-dd'),
            qr_code: `QR_${i + 1}_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
            created_at: format(subDays(new Date(), Math.floor(Math.random() * 365)), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
            updated_at: format(subDays(new Date(), Math.floor(Math.random() * 30)), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
            // Additional fields for comprehensive testing
            personality,
            notes: Math.random() > 0.7 ? `ملاحظات خاصة بـ ${name}` : '',
            emergency_contact:
                Math.random() > 0.5
                    ? phonePrefix +
                      Math.floor(Math.random() * 100000000)
                          .toString()
                          .padStart(8, '0')
                    : '',
            blood_type: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'][Math.floor(Math.random() * 8)],
            medical_conditions: Math.random() > 0.8 ? ['حساسية', 'ربو', 'سكري', 'ضغط دم'][Math.floor(Math.random() * 4)] : '',
        };

        users.push(user);
    }

    return users;
}

// Enhanced attendance generation with realistic patterns and comprehensive scenarios
export function generateMockAttendance(users: any[], daysBack = 180) {
    const attendanceRecords: any[] = [];
    const today = new Date();
    const markedByOptions = ['<EMAIL>', '<EMAIL>', '<EMAIL>', 'system'];

    // Create user-specific attendance patterns based on personality
    const userAttendancePatterns = users.map((user) => {
        let pattern;
        switch (user.personality) {
            case 'منتظم جداً':
                pattern = attendancePatterns.excellent;
                break;
            case 'منتظم':
                pattern = attendancePatterns.good;
                break;
            case 'متوسط الانتظام':
                pattern = attendancePatterns.average;
                break;
            case 'غير منتظم':
                pattern = attendancePatterns.poor;
                break;
            case 'متقطع الحضور':
                pattern = attendancePatterns.critical;
                break;
            default:
                pattern = attendancePatterns.average;
        }
        return {
            userId: user.id,
            userName: user.name,
            attendanceRate: pattern.min + Math.random() * (pattern.max - pattern.min),
            lastAttendance: null as Date | null,
            consecutiveAbsences: 0,
        };
    });

    for (let dayOffset = 0; dayOffset < daysBack; dayOffset++) {
        const date = subDays(today, dayOffset);
        const dateString = format(date, 'yyyy-MM-dd');
        const dayOfWeek = date.getDay();

        // Skip weekends and some random days (holidays, special events)
        if (dayOfWeek === 6 || dayOfWeek === 0) continue;
        if (Math.random() < 0.15) continue; // 15% chance of no meeting

        // Special events with higher attendance
        const isSpecialEvent = Math.random() < 0.1; // 10% chance of special event
        const eventName = isSpecialEvent ? specialEvents[Math.floor(Math.random() * specialEvents.length)] : null;

        userAttendancePatterns.forEach((userPattern) => {
            let willAttend = Math.random() < userPattern.attendanceRate;

            // Adjust attendance based on various factors
            if (isSpecialEvent) {
                willAttend = Math.random() < 0.9; // Higher attendance for special events
            }

            // Consecutive absence pattern - less likely to attend after multiple absences
            if (userPattern.consecutiveAbsences > 2) {
                willAttend = willAttend && Math.random() < 0.7;
            }

            // Recent attendance pattern - more likely to attend if attended recently
            if (userPattern.lastAttendance && dayOffset < 7) {
                willAttend = willAttend || Math.random() < 0.3;
            }

            // Create attendance record
            if (willAttend || Math.random() < 0.1) {
                // 10% chance to record even if not attending (for absent records)
                const record = {
                    id: `attendance_${willAttend ? 'present' : 'absent'}_${dateString}_${userPattern.userId}_${Math.random().toString(36).substring(2, 9)}`,
                    user_id: userPattern.userId,
                    user_name: userPattern.userName,
                    date: dateString,
                    present: willAttend,
                    marked_by: markedByOptions[Math.floor(Math.random() * markedByOptions.length)],
                    created_at: format(date, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
                    event_name: eventName,
                    notes: isSpecialEvent && willAttend ? `حضر ${eventName}` : '',
                    check_in_time: willAttend ? format(new Date(date.getTime() + Math.random() * 3600000), 'HH:mm') : null,
                };
                attendanceRecords.push(record);

                // Update user pattern
                if (willAttend) {
                    userPattern.lastAttendance = date;
                    userPattern.consecutiveAbsences = 0;
                } else {
                    userPattern.consecutiveAbsences++;
                }
            }
        });
    }

    return attendanceRecords;
}

// Generate comprehensive analytics data for dashboard and reports
export function generateAnalyticsData(users: any[], attendanceRecords: any[]) {
    const today = new Date();
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);

    return {
        totalUsers: users.length,
        activeUsers: users.filter((u) => new Date(u.first_attendance_date) <= today).length,
        newUsersThisMonth: users.filter((u) => new Date(u.created_at) >= thisMonth).length,
        birthdaysThisMonth: users.filter((u) => {
            const birthDate = new Date(u.birthdate);
            return birthDate.getMonth() === today.getMonth();
        }).length,
        attendanceStats: {
            totalSessions: [...new Set(attendanceRecords.map((r) => r.date))].length,
            totalAttendanceRecords: attendanceRecords.length,
            presentRecords: attendanceRecords.filter((r) => r.present).length,
            absentRecords: attendanceRecords.filter((r) => !r.present).length,
        },
        collegeDistribution: users.reduce((acc: any, user) => {
            acc[user.college] = (acc[user.college] || 0) + 1;
            return acc;
        }, {}),
        yearDistribution: users.reduce((acc: any, user) => {
            acc[`السنة ${user.year}`] = (acc[`السنة ${user.year}`] || 0) + 1;
            return acc;
        }, {}),
        genderDistribution: users.reduce((acc: any, user) => {
            acc[user.gender === 'male' ? 'ذكور' : 'إناث'] = (acc[user.gender === 'male' ? 'ذكور' : 'إناث'] || 0) + 1;
            return acc;
        }, {}),
    };
}

// Generate mock settings data for comprehensive testing
export function generateMockSettings() {
    return {
        auto_mark_absent: true,
        absent_cutoff_time: '21:00',
        birthday_notifications: true,
        notification_frequency: 'weekly' as 'weekly' | 'monthly' | 'end_of_month',
        whatsapp_message_template: 'كل سنة وانت طيب يا {{name}}! 🎉 ربنا يفرح قلبك دايماً',
        organization_name: 'كنيسة الشهيد مار جرجس',
        max_login_attempts: 5,
        session_timeout: 60,
        backup_frequency: 'weekly',
        data_retention_days: 365,
        email_notifications: true,
        sms_notifications: false,
        attendance_reminder_time: '18:00',
        late_arrival_threshold: 30, // minutes
        early_departure_threshold: 30, // minutes
    };
}

// Generate dashboard-specific analytics for comprehensive testing
export function generateDashboardAnalytics(users: any[], attendanceRecords: any[]) {
    const today = new Date();
    const todayString = format(today, 'yyyy-MM-dd');
    const weekAgo = subDays(today, 7);
    const monthAgo = subDays(today, 30);

    // Today's attendance
    const todayRecords = attendanceRecords.filter((r) => r.date === todayString);
    const presentToday = todayRecords.filter((r) => r.present).length;
    const absentToday = todayRecords.filter((r) => !r.present).length;

    // Weekly trends
    const weeklyRecords = attendanceRecords.filter((r) => new Date(r.date) >= weekAgo);
    const weeklyPresent = weeklyRecords.filter((r) => r.present).length;
    const weeklyTotal = weeklyRecords.length;
    const weeklyRate = weeklyTotal > 0 ? Math.round((weeklyPresent / weeklyTotal) * 100) : 0;

    // Monthly trends
    const monthlyRecords = attendanceRecords.filter((r) => new Date(r.date) >= monthAgo);
    const monthlyPresent = monthlyRecords.filter((r) => r.present).length;
    const monthlyTotal = monthlyRecords.length;
    const monthlyRate = monthlyTotal > 0 ? Math.round((monthlyPresent / monthlyTotal) * 100) : 0;

    // User attendance rates
    const userAttendanceRates = users.map((user) => {
        const userRecords = attendanceRecords.filter((r) => r.user_id === user.id);
        const userPresent = userRecords.filter((r) => r.present).length;
        const userTotal = userRecords.length;
        const rate = userTotal > 0 ? Math.round((userPresent / userTotal) * 100) : 0;
        return { ...user, attendanceRate: rate, totalSessions: userTotal };
    });

    const consistentAttendees = userAttendanceRates.filter((u) => u.attendanceRate >= 80).length;
    const redFlags = userAttendanceRates.filter((u) => u.attendanceRate < 50 && u.attendanceRate > 0).length;

    return {
        totalUsers: users.length,
        presentToday,
        absentToday,
        consistentAttendees,
        redFlags,
        attendanceRate: users.length > 0 ? Math.round((presentToday / users.length) * 100) : 0,
        weeklyAttendance: weeklyRate,
        monthlyAttendance: monthlyRate,
        userAttendanceRates,
        recentTrends: generateAttendanceTrends(attendanceRecords, 30),
        collegeStats: generateCollegeStats(users, attendanceRecords),
        yearStats: generateYearStats(users, attendanceRecords),
    };
}

// Generate attendance trends for charts
export function generateAttendanceTrends(attendanceRecords: any[], days: number) {
    const trends = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
        const date = subDays(today, i);
        const dateString = format(date, 'yyyy-MM-dd');
        const dayRecords = attendanceRecords.filter((r) => r.date === dateString);
        const present = dayRecords.filter((r) => r.present).length;
        const absent = dayRecords.filter((r) => !r.present).length;
        const total = present + absent;
        const rate = total > 0 ? Math.round((present / total) * 100) : 0;

        trends.push({
            date: dateString,
            present,
            absent,
            total,
            rate,
            dayName: format(date, 'EEEE'),
        });
    }

    return trends;
}

// Generate college-specific statistics
export function generateCollegeStats(users: any[], attendanceRecords: any[]) {
    const collegeGroups = users.reduce((acc: any, user) => {
        if (!acc[user.college]) {
            acc[user.college] = [];
        }
        acc[user.college].push(user);
        return acc;
    }, {});

    return Object.entries(collegeGroups).map(([college, collegeUsers]: [string, any]) => {
        const userIds = collegeUsers.map((u: any) => u.id);
        const collegeRecords = attendanceRecords.filter((r) => userIds.includes(r.user_id));
        const present = collegeRecords.filter((r) => r.present).length;
        const total = collegeRecords.length;
        const rate = total > 0 ? Math.round((present / total) * 100) : 0;

        return {
            college,
            totalUsers: collegeUsers.length,
            attendanceRate: rate,
            totalSessions: total,
            presentSessions: present,
        };
    });
}

// Generate year-specific statistics
export function generateYearStats(users: any[], attendanceRecords: any[]) {
    const yearGroups = users.reduce((acc: any, user) => {
        const year = `السنة ${user.year}`;
        if (!acc[year]) {
            acc[year] = [];
        }
        acc[year].push(user);
        return acc;
    }, {});

    return Object.entries(yearGroups).map(([year, yearUsers]: [string, any]) => {
        const userIds = yearUsers.map((u: any) => u.id);
        const yearRecords = attendanceRecords.filter((r) => userIds.includes(r.user_id));
        const present = yearRecords.filter((r) => r.present).length;
        const total = yearRecords.length;
        const rate = total > 0 ? Math.round((present / total) * 100) : 0;

        return {
            year,
            totalUsers: yearUsers.length,
            attendanceRate: rate,
            totalSessions: total,
            presentSessions: present,
        };
    });
}

// Generate specific attendance scenarios for comprehensive UI testing
export function generateAttendanceScenarios(users: any[]) {
    const scenarios = [];
    const today = new Date();

    // Scenario 1: Perfect attendance user
    const perfectUser = users[0];
    if (perfectUser) {
        for (let i = 0; i < 30; i++) {
            const date = subDays(today, i);
            if (date.getDay() !== 0 && date.getDay() !== 6) {
                // Skip weekends
                scenarios.push({
                    id: `perfect_${format(date, 'yyyy-MM-dd')}_${perfectUser.id}`,
                    user_id: perfectUser.id,
                    user_name: perfectUser.name,
                    date: format(date, 'yyyy-MM-dd'),
                    present: true,
                    marked_by: '<EMAIL>',
                    created_at: format(date, "yyyy-MM-dd'T'09:00:00.000'Z'"),
                    check_in_time: '09:00',
                    notes: 'حضور منتظم',
                });
            }
        }
    }

    // Scenario 2: Irregular attendance user
    const irregularUser = users[1];
    if (irregularUser) {
        for (let i = 0; i < 30; i++) {
            const date = subDays(today, i);
            if (date.getDay() !== 0 && date.getDay() !== 6) {
                const willAttend = Math.random() < 0.4; // 40% attendance
                scenarios.push({
                    id: `irregular_${format(date, 'yyyy-MM-dd')}_${irregularUser.id}`,
                    user_id: irregularUser.id,
                    user_name: irregularUser.name,
                    date: format(date, 'yyyy-MM-dd'),
                    present: willAttend,
                    marked_by: willAttend ? '<EMAIL>' : 'system',
                    created_at: format(date, "yyyy-MM-dd'T'09:00:00.000'Z'"),
                    check_in_time: willAttend ? format(new Date(date.getTime() + Math.random() * 7200000), 'HH:mm') : null,
                    notes: willAttend ? '' : 'غياب غير مبرر',
                });
            }
        }
    }

    // Scenario 3: Late arrivals
    const lateUser = users[2];
    if (lateUser) {
        for (let i = 0; i < 15; i++) {
            const date = subDays(today, i);
            if (date.getDay() !== 0 && date.getDay() !== 6) {
                scenarios.push({
                    id: `late_${format(date, 'yyyy-MM-dd')}_${lateUser.id}`,
                    user_id: lateUser.id,
                    user_name: lateUser.name,
                    date: format(date, 'yyyy-MM-dd'),
                    present: true,
                    marked_by: '<EMAIL>',
                    created_at: format(date, "yyyy-MM-dd'T'10:30:00.000'Z'"),
                    check_in_time: '10:30',
                    notes: 'وصول متأخر',
                    late_arrival: true,
                });
            }
        }
    }

    // Scenario 4: New member with recent start
    const newUser = users[3];
    if (newUser) {
        for (let i = 0; i < 7; i++) {
            // Only last week
            const date = subDays(today, i);
            if (date.getDay() !== 0 && date.getDay() !== 6) {
                scenarios.push({
                    id: `new_${format(date, 'yyyy-MM-dd')}_${newUser.id}`,
                    user_id: newUser.id,
                    user_name: newUser.name,
                    date: format(date, 'yyyy-MM-dd'),
                    present: true,
                    marked_by: '<EMAIL>',
                    created_at: format(date, "yyyy-MM-dd'T'09:15:00.000'Z'"),
                    check_in_time: '09:15',
                    notes: 'عضو جديد',
                    first_time: i === 6,
                });
            }
        }
    }

    return scenarios;
}

// Generate edge cases for testing
export function generateAttendanceEdgeCases(users: any[]) {
    const edgeCases = [];
    const today = new Date();

    // Edge case 1: Same user marked multiple times on same day
    const duplicateUser = users[4];
    if (duplicateUser) {
        const date = format(today, 'yyyy-MM-dd');
        edgeCases.push(
            {
                id: `duplicate1_${date}_${duplicateUser.id}`,
                user_id: duplicateUser.id,
                user_name: duplicateUser.name,
                date,
                present: false,
                marked_by: 'system',
                created_at: format(today, "yyyy-MM-dd'T'08:00:00.000'Z'"),
                notes: 'تسجيل غياب تلقائي',
            },
            {
                id: `duplicate2_${date}_${duplicateUser.id}`,
                user_id: duplicateUser.id,
                user_name: duplicateUser.name,
                date,
                present: true,
                marked_by: '<EMAIL>',
                created_at: format(today, "yyyy-MM-dd'T'09:30:00.000'Z'"),
                check_in_time: '09:30',
                notes: 'تصحيح - وصل متأخر',
                correction: true,
            },
        );
    }

    // Edge case 2: Very long absence period
    const absentUser = users[5];
    if (absentUser) {
        for (let i = 1; i <= 14; i++) {
            // 2 weeks absent
            const date = subDays(today, i);
            if (date.getDay() !== 0 && date.getDay() !== 6) {
                edgeCases.push({
                    id: `absent_${format(date, 'yyyy-MM-dd')}_${absentUser.id}`,
                    user_id: absentUser.id,
                    user_name: absentUser.name,
                    date: format(date, 'yyyy-MM-dd'),
                    present: false,
                    marked_by: 'system',
                    created_at: format(date, "yyyy-MM-dd'T'21:00:00.000'Z'"),
                    notes: 'غياب مطول - يحتاج متابعة',
                    long_absence: true,
                });
            }
        }
    }

    return edgeCases;
}

// Generate specific user profile variations for comprehensive testing
export function generateUserProfileVariations() {
    const variations = [];
    const today = new Date();

    // Profile 1: Long-time member with extensive history
    variations.push({
        id: 'longtime_member_001',
        name: 'أنطونيوس جرجس مجدي',
        phone: '**********8',
        gender: 'male' as const,
        year: 4 as const,
        college: 'كلية الطب',
        department: 'طب عام',
        birthdate: format(new Date(1998, 2, 15), 'yyyy-MM-dd'),
        address: 'شارع الجمهورية، وسط البلد، القاهرة',
        facebook_url: 'https://facebook.com/antonios.george',
        first_attendance_date: format(subDays(today, 1095), 'yyyy-MM-dd'), // 3 years ago
        qr_code: 'QR_LONGTIME_001',
        created_at: format(subDays(today, 1095), "yyyy-MM-dd'T'10:00:00.000'Z'"),
        updated_at: format(subDays(today, 1), "yyyy-MM-dd'T'15:30:00.000'Z'"),
        personality: 'عضو مؤسس',
        notes: 'عضو قيادي في الخدمة، له تاريخ طويل مع الكنيسة',
        emergency_contact: '01098765432',
        blood_type: 'O+',
        medical_conditions: '',
        leadership_role: 'رئيس مجلس الشباب',
        volunteer_hours: 150,
    });

    // Profile 2: New member with minimal data
    variations.push({
        id: 'new_member_001',
        name: 'مريم يوسف حنا',
        phone: '01156789012',
        gender: 'female' as const,
        year: 1 as const,
        college: 'كلية الصيدلة',
        department: 'صيدلة إكلينيكية',
        birthdate: format(new Date(2005, 8, 22), 'yyyy-MM-dd'),
        address: 'مدينة نصر، القاهرة الجديدة',
        facebook_url: '',
        first_attendance_date: format(subDays(today, 7), 'yyyy-MM-dd'), // Last week
        qr_code: 'QR_NEW_001',
        created_at: format(subDays(today, 7), "yyyy-MM-dd'T'18:00:00.000'Z'"),
        updated_at: format(subDays(today, 7), "yyyy-MM-dd'T'18:00:00.000'Z'"),
        personality: 'جديد في النظام',
        notes: 'عضو جديد، يحتاج إلى متابعة ودمج',
        emergency_contact: '',
        blood_type: 'A+',
        medical_conditions: '',
        orientation_completed: false,
        mentor_assigned: 'أنطونيوس جرجس مجدي',
    });

    // Profile 3: Problematic attendance pattern
    variations.push({
        id: 'irregular_member_001',
        name: 'مارك بولس عبدالله',
        phone: '01234567890',
        gender: 'male' as const,
        year: 3 as const,
        college: 'كلية الهندسة',
        department: 'حاسبات',
        birthdate: format(new Date(2001, 11, 5), 'yyyy-MM-dd'),
        address: 'الزمالك، القاهرة',
        facebook_url: 'https://facebook.com/mark.paul.abdullah',
        first_attendance_date: format(subDays(today, 365), 'yyyy-MM-dd'), // 1 year ago
        qr_code: 'QR_IRREGULAR_001',
        created_at: format(subDays(today, 365), "yyyy-MM-dd'T'12:00:00.000'Z'"),
        updated_at: format(subDays(today, 30), "yyyy-MM-dd'T'14:20:00.000'Z'"),
        personality: 'غير منتظم',
        notes: 'حضور متقطع، يحتاج متابعة خاصة. آخر حضور منذ شهر',
        emergency_contact: '01087654321',
        blood_type: 'B-',
        medical_conditions: '',
        attendance_warnings: 3,
        last_contact_date: format(subDays(today, 15), 'yyyy-MM-dd'),
        follow_up_needed: true,
    });

    // Profile 4: Special needs member
    variations.push({
        id: 'special_needs_001',
        name: 'سوسنة دانيال فهيم',
        phone: '01098765432',
        gender: 'female' as const,
        year: 2 as const,
        college: 'كلية التربية',
        department: 'تربية خاصة',
        birthdate: format(new Date(2003, 4, 18), 'yyyy-MM-dd'),
        address: 'المعادي، القاهرة',
        facebook_url: '',
        first_attendance_date: format(subDays(today, 180), 'yyyy-MM-dd'), // 6 months ago
        qr_code: 'QR_SPECIAL_001',
        created_at: format(subDays(today, 180), "yyyy-MM-dd'T'16:00:00.000'Z'"),
        updated_at: format(subDays(today, 5), "yyyy-MM-dd'T'11:45:00.000'Z'"),
        personality: 'منتظم',
        notes: 'تحتاج مساعدة في التنقل، لديها إعاقة حركية بسيطة',
        emergency_contact: '01123456789',
        blood_type: 'AB+',
        medical_conditions: 'إعاقة حركية بسيطة',
        special_accommodations: 'مقعد قريب من المدخل، مساعدة في التنقل',
        accessibility_needs: true,
        support_person: 'والدة العضو',
    });

    // Profile 5: International/Exchange student
    variations.push({
        id: 'international_001',
        name: 'جون مايكل سميث',
        phone: '01567890123',
        gender: 'male' as const,
        year: 2 as const,
        college: 'كلية الألسن',
        department: 'لغة عربية',
        birthdate: format(new Date(2002, 6, 10), 'yyyy-MM-dd'),
        address: 'مصر الجديدة، هليوبوليس',
        facebook_url: 'https://facebook.com/john.michael.smith',
        first_attendance_date: format(subDays(today, 90), 'yyyy-MM-dd'), // 3 months ago
        qr_code: 'QR_INTL_001',
        created_at: format(subDays(today, 90), "yyyy-MM-dd'T'14:30:00.000'Z'"),
        updated_at: format(subDays(today, 2), "yyyy-MM-dd'T'09:15:00.000'Z'"),
        personality: 'منتظم',
        notes: 'طالب تبادل من أمريكا، يتعلم العربية والثقافة المصرية',
        emergency_contact: '******-123-4567',
        blood_type: 'O-',
        medical_conditions: '',
        nationality: 'أمريكي',
        visa_expiry: format(addDays(today, 180), 'yyyy-MM-dd'),
        language_preference: 'English/Arabic',
        cultural_orientation: true,
    });

    return variations;
}

// Generate users with specific birthday distributions for birthday page testing
export function generateBirthdayTestUsers() {
    const birthdayUsers = [];
    const today = new Date();
    const currentYear = today.getFullYear();

    // Users with birthdays this week
    for (let i = 0; i < 3; i++) {
        const birthdayDate = addDays(today, i);
        birthdayUsers.push({
            id: `birthday_thisweek_${i}`,
            name: `عضو عيد ميلاد ${i + 1}`,
            phone: `**********${i}`,
            gender: i % 2 === 0 ? 'male' : ('female' as const),
            year: ((i % 4) + 1) as 1 | 2 | 3 | 4,
            college: colleges[i % colleges.length],
            department: 'قسم عام',
            birthdate: format(new Date(currentYear - 20, birthdayDate.getMonth(), birthdayDate.getDate()), 'yyyy-MM-dd'),
            address: addresses[i % addresses.length],
            facebook_url: `https://facebook.com/birthday${i}`,
            first_attendance_date: format(subDays(today, 365), 'yyyy-MM-dd'),
            qr_code: `QR_BIRTHDAY_${i}`,
            created_at: format(subDays(today, 365), "yyyy-MM-dd'T'10:00:00.000'Z'"),
            updated_at: format(today, "yyyy-MM-dd'T'10:00:00.000'Z'"),
            personality: 'منتظم',
            notes: `عيد ميلاد في ${format(birthdayDate, 'dd/MM')}`,
            emergency_contact: '',
            blood_type: 'O+',
            medical_conditions: '',
        });
    }

    // Users with birthdays this month
    for (let i = 0; i < 5; i++) {
        const dayOfMonth = 5 + i * 5; // Days 5, 10, 15, 20, 25
        birthdayUsers.push({
            id: `birthday_thismonth_${i}`,
            name: `عضو شهر ${today.getMonth() + 1} - ${i + 1}`,
            phone: `**********${i}`,
            gender: i % 2 === 0 ? 'female' : ('male' as const),
            year: ((i % 4) + 1) as 1 | 2 | 3 | 4,
            college: colleges[(i + 3) % colleges.length],
            department: 'قسم عام',
            birthdate: format(new Date(currentYear - 21, today.getMonth(), dayOfMonth), 'yyyy-MM-dd'),
            address: addresses[(i + 5) % addresses.length],
            facebook_url: '',
            first_attendance_date: format(subDays(today, 200), 'yyyy-MM-dd'),
            qr_code: `QR_MONTH_${i}`,
            created_at: format(subDays(today, 200), "yyyy-MM-dd'T'10:00:00.000'Z'"),
            updated_at: format(today, "yyyy-MM-dd'T'10:00:00.000'Z'"),
            personality: 'متوسط الانتظام',
            notes: '',
            emergency_contact: '',
            blood_type: 'A+',
            medical_conditions: '',
        });
    }

    return birthdayUsers;
}

// Generate comprehensive reports data for reports page
export function generateReportsData(users: any[], attendanceRecords: any[]) {
    const today = new Date();
    const lastMonth = subDays(today, 30);
    const lastQuarter = subDays(today, 90);
    const lastYear = subDays(today, 365);

    // Monthly comparison data
    const monthlyComparison = [];
    for (let i = 0; i < 12; i++) {
        const monthStart = new Date(today.getFullYear(), today.getMonth() - i, 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() - i + 1, 0);
        const monthRecords = attendanceRecords.filter((r) => {
            const recordDate = new Date(r.date);
            return recordDate >= monthStart && recordDate <= monthEnd;
        });

        const present = monthRecords.filter((r) => r.present).length;
        const total = monthRecords.length;
        const rate = total > 0 ? Math.round((present / total) * 100) : 0;

        monthlyComparison.push({
            month: format(monthStart, 'MMMM yyyy'),
            monthShort: format(monthStart, 'MMM'),
            present,
            absent: total - present,
            total,
            rate,
            sessions: [...new Set(monthRecords.map((r) => r.date))].length,
        });
    }

    // Detailed user performance analysis
    const userPerformanceAnalysis = users.map((user) => {
        const userRecords = attendanceRecords.filter((r) => r.user_id === user.id);
        const present = userRecords.filter((r) => r.present).length;
        const total = userRecords.length;
        const rate = total > 0 ? Math.round((present / total) * 100) : 0;

        // Calculate streaks
        let currentStreak = 0;
        let longestStreak = 0;
        let tempStreak = 0;

        const sortedRecords = userRecords.filter((r) => r.present).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

        for (let i = 0; i < sortedRecords.length; i++) {
            if (i === 0 || new Date(sortedRecords[i - 1].date).getTime() - new Date(sortedRecords[i].date).getTime() <= 7 * 24 * 60 * 60 * 1000) {
                tempStreak++;
                if (i === 0) currentStreak = tempStreak;
            } else {
                longestStreak = Math.max(longestStreak, tempStreak);
                tempStreak = 1;
            }
        }
        longestStreak = Math.max(longestStreak, tempStreak);

        // Recent activity
        const recentRecords = userRecords.filter((r) => new Date(r.date) >= lastMonth);
        const recentRate = recentRecords.length > 0 ? Math.round((recentRecords.filter((r) => r.present).length / recentRecords.length) * 100) : 0;

        return {
            ...user,
            attendanceRate: rate,
            totalSessions: total,
            presentSessions: present,
            absentSessions: total - present,
            currentStreak,
            longestStreak,
            recentRate,
            lastAttendance:
                userRecords.filter((r) => r.present).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0]?.date || null,
            riskLevel: rate < 50 ? 'high' : rate < 70 ? 'medium' : 'low',
            trend: recentRate > rate ? 'improving' : recentRate < rate ? 'declining' : 'stable',
        };
    });

    // College performance comparison
    const collegePerformance = generateCollegeStats(users, attendanceRecords).map((college) => ({
        ...college,
        averageAge:
            users
                .filter((u) => u.college === college.college)
                .reduce((sum, u) => sum + (new Date().getFullYear() - new Date(u.birthdate).getFullYear()), 0) /
            users.filter((u) => u.college === college.college).length,
        genderDistribution: users
            .filter((u) => u.college === college.college)
            .reduce((acc: any, u) => {
                acc[u.gender] = (acc[u.gender] || 0) + 1;
                return acc;
            }, {}),
    }));

    // Attendance patterns by day of week
    const dayOfWeekAnalysis = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map((day, index) => {
        const dayRecords = attendanceRecords.filter((r) => new Date(r.date).getDay() === index);
        const present = dayRecords.filter((r) => r.present).length;
        const total = dayRecords.length;
        const rate = total > 0 ? Math.round((present / total) * 100) : 0;

        return {
            day,
            dayArabic: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'][index],
            present,
            absent: total - present,
            total,
            rate,
            sessions: [...new Set(dayRecords.map((r) => r.date))].length,
        };
    });

    // Special events analysis
    const specialEventsAnalysis = attendanceRecords
        .filter((r) => r.event_name)
        .reduce((acc: any, record) => {
            const eventName = record.event_name;
            if (!acc[eventName]) {
                acc[eventName] = {
                    eventName,
                    totalAttendees: 0,
                    sessions: new Set(),
                    averageAttendance: 0,
                };
            }
            if (record.present) {
                acc[eventName].totalAttendees++;
            }
            acc[eventName].sessions.add(record.date);
            return acc;
        }, {});

    Object.values(specialEventsAnalysis).forEach((event: any) => {
        event.sessions = event.sessions.size;
        event.averageAttendance = Math.round(event.totalAttendees / event.sessions);
    });

    return {
        monthlyComparison: monthlyComparison.reverse(),
        userPerformanceAnalysis,
        collegePerformance,
        dayOfWeekAnalysis,
        specialEventsAnalysis: Object.values(specialEventsAnalysis),
        summary: {
            totalUsers: users.length,
            totalSessions: [...new Set(attendanceRecords.map((r) => r.date))].length,
            averageAttendanceRate: Math.round(userPerformanceAnalysis.reduce((sum, u) => sum + u.attendanceRate, 0) / userPerformanceAnalysis.length),
            highPerformers: userPerformanceAnalysis.filter((u) => u.attendanceRate >= 80).length,
            atRiskUsers: userPerformanceAnalysis.filter((u) => u.attendanceRate < 50).length,
            mostActiveCollege: collegePerformance.sort((a, b) => b.attendanceRate - a.attendanceRate)[0]?.college || '',
            bestAttendanceDay: dayOfWeekAnalysis.sort((a, b) => b.rate - a.rate)[0]?.dayArabic || '',
        },
    };
}

// Generate trend analysis for charts and visualizations
export function generateTrendAnalysis(attendanceRecords: any[], days: number = 90) {
    const trends = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
        const date = subDays(today, i);
        const dateString = format(date, 'yyyy-MM-dd');
        const dayRecords = attendanceRecords.filter((r) => r.date === dateString);
        const present = dayRecords.filter((r) => r.present).length;
        const absent = dayRecords.filter((r) => !r.present).length;
        const total = present + absent;
        const rate = total > 0 ? Math.round((present / total) * 100) : 0;

        // Calculate moving average (7-day)
        const weekRecords = [];
        for (let j = 0; j < 7; j++) {
            const weekDate = subDays(date, j);
            const weekDateString = format(weekDate, 'yyyy-MM-dd');
            const weekDayRecords = attendanceRecords.filter((r) => r.date === weekDateString);
            weekRecords.push(...weekDayRecords);
        }
        const weekPresent = weekRecords.filter((r) => r.present).length;
        const weekTotal = weekRecords.length;
        const movingAverage = weekTotal > 0 ? Math.round((weekPresent / weekTotal) * 100) : 0;

        trends.push({
            date: dateString,
            present,
            absent,
            total,
            rate,
            movingAverage,
            dayName: format(date, 'EEEE'),
            dayNameArabic: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'][date.getDay()],
            isWeekend: date.getDay() === 0 || date.getDay() === 6,
            hasSpecialEvent: attendanceRecords.some((r) => r.date === dateString && r.event_name),
        });
    }

    return trends;
}

// Enhanced initialization with comprehensive data including scenarios and edge cases
export function initializeMockData() {
    const regularUsers = generateMockUsers(180);
    const profileVariations = generateUserProfileVariations();
    const birthdayUsers = generateBirthdayTestUsers();

    // Combine all user data
    const allUsers = [...regularUsers, ...profileVariations, ...birthdayUsers];

    const attendanceRecords = generateMockAttendance(allUsers, 180);
    const attendanceScenarios = generateAttendanceScenarios(allUsers);
    const attendanceEdgeCases = generateAttendanceEdgeCases(allUsers);

    // Combine all attendance data
    const allAttendanceRecords = [...attendanceRecords, ...attendanceScenarios, ...attendanceEdgeCases];

    const analytics = generateAnalyticsData(allUsers, allAttendanceRecords);
    const dashboardAnalytics = generateDashboardAnalytics(allUsers, allAttendanceRecords);
    const reportsData = generateReportsData(allUsers, allAttendanceRecords);
    const trendAnalysis = generateTrendAnalysis(allAttendanceRecords, 90);
    const settings = generateMockSettings();

    return {
        users: allUsers,
        attendanceRecords: allAttendanceRecords,
        analytics,
        dashboardAnalytics,
        reportsData,
        trendAnalysis,
        settings,
        scenarios: {
            attendanceScenarios,
            attendanceEdgeCases,
            profileVariations,
            birthdayUsers,
        },
    };
}
