# Technical Specification Document

## Arsanios Youth Meeting Attendance Management System

### Table of Contents

1. [Project Overview Enhancement](#project-overview-enhancement)
2. [System Architecture](#system-architecture)
3. [API Routes Documentation](#api-routes-documentation)
4. [Database Design](#database-design)
5. [Data Models and Structures](#data-models-and-structures)
6. [Laravel Implementation Plan](#laravel-implementation-plan)
7. [Authentication & Authorization](#authentication--authorization)
8. [Frontend Integration](#frontend-integration)
9. [Performance Requirements](#performance-requirements)
10. [Security Considerations](#security-considerations)

---

## 1. Project Overview Enhancement

### 1.1 Project Description

A comprehensive web-based attendance management system for "اجتماع الأنبا أرسانيوس لشباب كليتي علوم وتربية" built with Laravel 12 backend and React/Inertia.js frontend. The system provides real-time attendance tracking, QR code integration, automated reporting, and birthday management with WhatsApp integration.

### 1.2 Technical Objectives

- **Real-time Performance**: Dashboard loads under 2 seconds with live attendance updates
- **Scalability**: Support up to 1000 concurrent users and 10,000+ member records
- **Mobile-First**: Responsive design optimized for QR scanning on mobile devices
- **Data Integrity**: Comprehensive validation and backup systems
- **Multilingual Support**: RTL Arabic and LTR English interfaces
- **Integration Ready**: WhatsApp API, Typesense search, and export capabilities

### 1.3 Key Features Analysis (Based on Frontend UI)

- **Dashboard**: Real-time analytics with 6 key metrics, trend analysis, and quick actions
- **Attendance Management**: Multi-method attendance (search, manual, QR, bulk operations)
- **User Management**: Comprehensive member profiles with academic and personal data
- **Reports**: Advanced filtering, multiple export formats (PDF, Excel, CSV)
- **Birthday Management**: Calendar view, automated WhatsApp greetings
- **Settings**: Configurable attendance rules, notification templates, data management

---

## 2. System Architecture

### 2.1 Technology Stack

- **Backend**: Laravel 12 (PHP 8.2+)
- **Frontend**: React 18 + TypeScript + Inertia.js 2.0
- **Database**: PostgreSQL (Neon Cloud)
- **Search Engine**: Typesense
- **Styling**: Tailwind CSS + Shadcn/ui
- **State Management**: Zustand
- **Authentication**: Laravel Sanctum
- **File Storage**: Laravel Storage (local/cloud)
- **Queue System**: Laravel Queues (Redis/Database)

### 2.2 Application Structure

```
├── app/
│   ├── Http/Controllers/Api/
│   ├── Models/
│   ├── Services/
│   ├── Jobs/
│   └── Notifications/
├── database/
│   ├── migrations/
│   ├── seeders/
│   └── factories/
├── resources/js/
│   ├── pages/
│   ├── components/
│   ├── stores/
│   └── types/
└── routes/
    ├── api.php
    └── web.php
```

---

## 3. API Routes Documentation

### 3.1 Authentication Routes

```php
// Authentication (Laravel Sanctum)
POST   /api/auth/login              // Login and issue token
POST   /api/auth/logout             // Revoke token
POST   /api/auth/refresh            // Refresh token
GET    /api/auth/user               // Get authenticated user
```

### 3.2 User Management Routes

```php
// Users CRUD
GET    /api/users                   // List users (paginated, filtered)
POST   /api/users                   // Create new user
GET    /api/users/{id}              // Get user details
PUT    /api/users/{id}              // Update user
DELETE /api/users/{id}              // Delete user
GET    /api/users/{id}/qr           // Generate QR code
POST   /api/users/bulk              // Bulk operations
GET    /api/users/search            // Typesense search
```

### 3.3 Attendance Management Routes

```php
// Attendance Operations
POST   /api/attendance/mark         // Mark attendance (QR/Manual)
GET    /api/attendance              // Get attendance records
GET    /api/attendance/today        // Today's attendance
GET    /api/attendance/user/{id}    // User attendance history
POST   /api/attendance/bulk         // Bulk attendance marking
PUT    /api/attendance/{id}         // Update attendance record
DELETE /api/attendance/{id}         // Delete attendance record
```

### 3.4 Analytics & Reports Routes

```php
// Dashboard Analytics
GET    /api/analytics/dashboard     // Dashboard statistics
GET    /api/analytics/trends        // Attendance trends
GET    /api/analytics/performance   // Performance metrics

// Reports
GET    /api/reports/weekly          // Weekly reports
GET    /api/reports/monthly         // Monthly reports
GET    /api/reports/yearly          // Yearly reports
POST   /api/reports/custom          // Custom report generation
GET    /api/reports/export/{format} // Export reports (PDF/Excel/CSV)
```

### 3.5 Birthday Management Routes

```php
// Birthday Management
GET    /api/birthdays               // List birthdays (filtered)
GET    /api/birthdays/upcoming      // Upcoming birthdays
POST   /api/birthdays/greetings     // Send WhatsApp greetings
GET    /api/birthdays/calendar      // Calendar view data
```

### 3.6 Settings & Configuration Routes

```php
// System Settings
GET    /api/settings                // Get all settings
PUT    /api/settings                // Update settings
POST   /api/settings/backup         // Create data backup
POST   /api/settings/restore        // Restore from backup
```

### 3.7 Data Import/Export Routes

```php
// Data Management
POST   /api/data/import/users       // Import users from Excel
POST   /api/data/import/attendance  // Import attendance data
GET    /api/data/export/users       // Export users
GET    /api/data/export/attendance  // Export attendance
GET    /api/data/template/{type}    // Download import templates
```

---

## 4. Database Design

### 4.1 Enhanced Users Table

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    gender user_gender_enum NOT NULL,
    birth_date DATE NOT NULL,
    address TEXT NOT NULL,
    facebook_url VARCHAR(255),
    college VARCHAR(100) NOT NULL,
    academic_year SMALLINT CHECK (academic_year BETWEEN 1 AND 4),
    department VARCHAR(100) NOT NULL,
    first_attendance_date DATE NOT NULL DEFAULT CURRENT_DATE,
    qr_token VARCHAR(100) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Indexes for performance
    INDEX idx_users_phone (phone),
    INDEX idx_users_qr_token (qr_token),
    INDEX idx_users_college_year (college, academic_year),
    INDEX idx_users_birth_date (birth_date),
    INDEX idx_users_active (is_active)
);

-- Enum for gender
CREATE TYPE user_gender_enum AS ENUM ('male', 'female');
```

### 4.2 Enhanced Attendances Table

```sql
CREATE TABLE attendances (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    meeting_date DATE NOT NULL,
    status attendance_status_enum NOT NULL,
    marked_by VARCHAR(255),
    marked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    method attendance_method_enum DEFAULT 'manual',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Unique constraint to prevent duplicate attendance
    UNIQUE(user_id, meeting_date),

    -- Indexes for performance
    INDEX idx_attendance_date (meeting_date),
    INDEX idx_attendance_user_date (user_id, meeting_date),
    INDEX idx_attendance_status (status)
);

-- Enums for attendance
CREATE TYPE attendance_status_enum AS ENUM ('present', 'absent', 'late', 'excused');
CREATE TYPE attendance_method_enum AS ENUM ('manual', 'qr_code', 'bulk', 'auto');
```

### 4.3 Settings Table

```sql
CREATE TABLE settings (
    id BIGSERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_settings_key (key),
    INDEX idx_settings_category (category)
);
```

### 4.4 Notifications Table

```sql
CREATE TABLE notifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    type notification_type_enum NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    sent_at TIMESTAMP,
    status notification_status_enum DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_notifications_user (user_id),
    INDEX idx_notifications_type (type),
    INDEX idx_notifications_status (status),
    INDEX idx_notifications_sent_at (sent_at)
);

-- Enums for notifications
CREATE TYPE notification_type_enum AS ENUM ('birthday', 'reminder', 'announcement', 'follow_up');
CREATE TYPE notification_status_enum AS ENUM ('pending', 'sent', 'failed', 'cancelled');
```

### 4.5 Import Logs Table

```sql
CREATE TABLE import_logs (
    id BIGSERIAL PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT,
    import_type VARCHAR(50) NOT NULL,
    total_records INTEGER DEFAULT 0,
    successful_records INTEGER DEFAULT 0,
    failed_records INTEGER DEFAULT 0,
    errors JSONB,
    imported_by VARCHAR(255),
    status import_status_enum DEFAULT 'processing',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,

    INDEX idx_import_logs_type (import_type),
    INDEX idx_import_logs_status (status),
    INDEX idx_import_logs_date (started_at)
);

-- Enum for import status
CREATE TYPE import_status_enum AS ENUM ('processing', 'completed', 'failed', 'cancelled');
```

### 4.6 Audit Trail Table

```sql
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id BIGINT,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_audit_logs_user (user_id),
    INDEX idx_audit_logs_table (table_name),
    INDEX idx_audit_logs_action (action),
    INDEX idx_audit_logs_date (created_at)
);
```

---

## 5. Data Models and Structures

### 5.1 User Model (Laravel Eloquent)

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name', 'phone', 'gender', 'birth_date', 'address',
        'facebook_url', 'college', 'academic_year', 'department',
        'first_attendance_date', 'qr_token', 'is_active', 'notes'
    ];

    protected $casts = [
        'birth_date' => 'date',
        'first_attendance_date' => 'date',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $hidden = ['qr_token'];

    // Relationships
    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    // Accessors & Mutators
    public function getAgeAttribute(): int
    {
        return $this->birth_date->age;
    }

    public function getAttendanceRateAttribute(): float
    {
        $total = $this->attendances()->count();
        if ($total === 0) return 0;

        $present = $this->attendances()->where('status', 'present')->count();
        return round(($present / $total) * 100, 2);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByYear($query, $year)
    {
        return $query->where('academic_year', $year);
    }

    public function scopeByCollege($query, $college)
    {
        return $query->where('college', 'like', "%{$college}%");
    }

    public function scopeBirthdayThisMonth($query)
    {
        return $query->whereMonth('birth_date', now()->month);
    }

    // Methods
    public function generateQrToken(): string
    {
        $this->qr_token = 'ARSANIOS-' . $this->id . '-' . Str::random(8);
        $this->save();
        return $this->qr_token;
    }

    public function markAttendance(string $status = 'present', string $markedBy = null): Attendance
    {
        return $this->attendances()->updateOrCreate(
            ['meeting_date' => now()->toDateString()],
            [
                'status' => $status,
                'marked_by' => $markedBy,
                'marked_at' => now(),
                'method' => 'manual'
            ]
        );
    }
}
```

### 5.2 Attendance Model

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Attendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'meeting_date', 'status', 'marked_by',
        'marked_at', 'method', 'notes'
    ];

    protected $casts = [
        'meeting_date' => 'date',
        'marked_at' => 'datetime',
        'created_at' => 'datetime',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->where('meeting_date', now()->toDateString());
    }

    public function scopePresent($query)
    {
        return $query->where('status', 'present');
    }

    public function scopeAbsent($query)
    {
        return $query->where('status', 'absent');
    }

    public function scopeByDateRange($query, $from, $to)
    {
        return $query->whereBetween('meeting_date', [$from, $to]);
    }

    public function scopeByMethod($query, $method)
    {
        return $query->where('method', $method);
    }
}
```

### 5.3 Validation Rules

```php
// User Validation Rules
public static function validationRules(): array
{
    return [
        'name' => 'required|string|min:2|max:255|regex:/^[\p{Arabic}\s]+$/u',
        'phone' => 'required|string|size:11|regex:/^01[0-9]{9}$/|unique:users,phone',
        'gender' => 'required|in:male,female',
        'birth_date' => 'required|date|before:today|after:' . now()->subYears(35)->toDateString(),
        'address' => 'required|string|min:10|max:500',
        'facebook_url' => 'nullable|url|regex:/^https?:\/\/(www\.)?(facebook|fb)\.com\/.*$/',
        'college' => 'required|string|min:2|max:100',
        'academic_year' => 'required|integer|between:1,4',
        'department' => 'required|string|min:2|max:100',
        'first_attendance_date' => 'required|date|before_or_equal:today',
    ];
}

// Attendance Validation Rules
public static function attendanceValidationRules(): array
{
    return [
        'user_id' => 'required|exists:users,id',
        'meeting_date' => 'required|date|before_or_equal:today',
        'status' => 'required|in:present,absent,late,excused',
        'method' => 'required|in:manual,qr_code,bulk,auto',
        'notes' => 'nullable|string|max:500',
    ];
}
```

---

## 6. Laravel Implementation Plan

### 6.1 Required Laravel Packages

```bash
# Core Dependencies (already installed)
composer require laravel/framework:^12.0
composer require inertiajs/inertia-laravel:^2.0
composer require tightenco/ziggy:^2.4

# Additional Required Packages
composer require laravel/sanctum                    # API Authentication
composer require spatie/laravel-permission         # Role & Permission Management
composer require maatwebsite/laravel-excel         # Excel Import/Export
composer require barryvdh/laravel-dompdf          # PDF Generation
composer require intervention/image                # Image Processing (QR codes)
composer require simplesoftwareio/simple-qrcode    # QR Code Generation
composer require laravel/horizon                   # Queue Management
composer require spatie/laravel-backup            # Database Backup
composer require spatie/laravel-activitylog       # Audit Trail
composer require typesense/typesense-php          # Search Integration

# Development Dependencies
composer require --dev laravel/telescope          # Debugging
composer require --dev barryvdh/laravel-debugbar  # Debug Bar
```

### 6.2 Service Classes Structure

```php
// App/Services/AttendanceService.php
class AttendanceService
{
    public function markAttendance(array $data): Attendance;
    public function bulkMarkAttendance(array $userIds, string $status): Collection;
    public function getAttendanceStats(Carbon $date): array;
    public function generateAttendanceReport(array $filters): array;
}

// App/Services/UserService.php
class UserService
{
    public function createUser(array $data): User;
    public function updateUser(User $user, array $data): User;
    public function generateQrCode(User $user): string;
    public function importUsersFromExcel(UploadedFile $file): ImportResult;
}

// App/Services/AnalyticsService.php
class AnalyticsService
{
    public function getDashboardStats(): array;
    public function getAttendanceTrends(int $days = 30): array;
    public function getUserPerformanceMetrics(): array;
    public function generateCustomReport(array $filters): array;
}

// App/Services/NotificationService.php
class NotificationService
{
    public function sendBirthdayGreetings(): void;
    public function sendWhatsAppMessage(string $phone, string $message): bool;
    public function scheduleReminders(): void;
}
```

### 6.3 Job Classes for Background Processing

```php
// App/Jobs/ProcessAttendanceImport.php
class ProcessAttendanceImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(AttendanceService $service): void;
}

// App/Jobs/SendBirthdayNotifications.php
class SendBirthdayNotifications implements ShouldQueue
{
    public function handle(NotificationService $service): void;
}

// App/Jobs/GenerateMonthlyReport.php
class GenerateMonthlyReport implements ShouldQueue
{
    public function handle(AnalyticsService $service): void;
}
```

### 6.4 API Controllers Structure

```php
// App/Http/Controllers/Api/UserController.php
class UserController extends Controller
{
    public function index(Request $request): JsonResponse;
    public function store(StoreUserRequest $request): JsonResponse;
    public function show(User $user): JsonResponse;
    public function update(UpdateUserRequest $request, User $user): JsonResponse;
    public function destroy(User $user): JsonResponse;
    public function generateQr(User $user): JsonResponse;
    public function search(Request $request): JsonResponse;
}

// App/Http/Controllers/Api/AttendanceController.php
class AttendanceController extends Controller
{
    public function index(Request $request): JsonResponse;
    public function store(StoreAttendanceRequest $request): JsonResponse;
    public function markByQr(MarkAttendanceRequest $request): JsonResponse;
    public function bulkMark(BulkAttendanceRequest $request): JsonResponse;
    public function todayStats(): JsonResponse;
    public function userHistory(User $user): JsonResponse;
}
```

### 6.5 Middleware Configuration

```php
// API Rate Limiting
Route::middleware(['api', 'throttle:api'])->group(function () {
    // Standard API routes - 60 requests per minute
});

Route::middleware(['api', 'throttle:attendance'])->group(function () {
    // Attendance marking - 120 requests per minute
});

// Custom Middleware
// App/Http/Middleware/ValidateQrToken.php
class ValidateQrToken
{
    public function handle(Request $request, Closure $next): Response;
}

// App/Http/Middleware/AuditLog.php
class AuditLog
{
    public function handle(Request $request, Closure $next): Response;
}
```

### 6.6 Database Seeders

```php
// DatabaseSeeder.php
class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            SettingsSeeder::class,
            AdminUserSeeder::class,
            SampleUsersSeeder::class,
            AttendanceSeeder::class,
        ]);
    }
}

// SettingsSeeder.php
class SettingsSeeder extends Seeder
{
    public function run(): void
    {
        $defaultSettings = [
            'auto_mark_absent' => true,
            'absent_cutoff_time' => '21:00',
            'birthday_notifications' => true,
            'notification_frequency' => 'weekly',
            'whatsapp_template' => 'كل سنة وانت طيب يا {{name}}! 🎉',
            'meeting_days' => ['friday'], // Weekly meeting day
            'academic_year_start' => '2024-09-01',
        ];

        foreach ($defaultSettings as $key => $value) {
            Setting::create([
                'key' => $key,
                'value' => json_encode($value),
                'category' => 'general'
            ]);
        }
    }
}
```

---

## 7. Authentication & Authorization

### 7.1 Laravel Sanctum Configuration

```php
// config/sanctum.php
return [
    'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
        '%s%s',
        'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
        Sanctum::currentApplicationUrlWithPort()
    ))),

    'guard' => ['web'],
    'expiration' => 525600, // 1 year in minutes
    'token_prefix' => env('SANCTUM_TOKEN_PREFIX', ''),
    'middleware' => [
        'authenticate_session' => Laravel\Sanctum\Http\Middleware\AuthenticateSession::class,
        'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
        'validate_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
    ],
];

// API Authentication Controller
class AuthController extends Controller
{
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->validated();

        if (!Auth::attempt($credentials)) {
            return response()->json(['message' => 'Invalid credentials'], 401);
        }

        $user = Auth::user();
        $token = $user->createToken('api-token')->plainTextToken;

        return response()->json([
            'user' => $user,
            'token' => $token,
            'expires_at' => now()->addYear()
        ]);
    }

    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();
        return response()->json(['message' => 'Logged out successfully']);
    }
}
```

### 7.2 Role-Based Access Control

```php
// Using Spatie Laravel Permission
class Permission extends Spatie\Permission\Models\Permission {}
class Role extends Spatie\Permission\Models\Role {}

// Permissions Seeder
class PermissionsSeeder extends Seeder
{
    public function run(): void
    {
        $permissions = [
            'view_dashboard',
            'manage_users',
            'mark_attendance',
            'view_reports',
            'export_data',
            'manage_settings',
            'send_notifications',
            'view_analytics'
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles
        $admin = Role::create(['name' => 'admin']);
        $receptionist = Role::create(['name' => 'receptionist']);
        $followup = Role::create(['name' => 'followup']);

        // Assign permissions
        $admin->givePermissionTo(Permission::all());
        $receptionist->givePermissionTo(['view_dashboard', 'mark_attendance', 'view_reports']);
        $followup->givePermissionTo(['view_dashboard', 'view_reports', 'send_notifications']);
    }
}
```

---

## 8. Frontend Integration

### 8.1 TypeScript Interfaces (Enhanced)

```typescript
// resources/js/types/api.d.ts
export interface ApiUser {
    id: string;
    name: string;
    phone: string;
    gender: 'male' | 'female';
    birth_date: string;
    address: string;
    facebook_url?: string;
    college: string;
    academic_year: 1 | 2 | 3 | 4;
    department: string;
    first_attendance_date: string;
    qr_token: string;
    is_active: boolean;
    attendance_rate: number;
    age: number;
    created_at: string;
    updated_at: string;
}

export interface ApiAttendance {
    id: string;
    user_id: string;
    meeting_date: string;
    status: 'present' | 'absent' | 'late' | 'excused';
    marked_by: string;
    marked_at: string;
    method: 'manual' | 'qr_code' | 'bulk' | 'auto';
    notes?: string;
    user: ApiUser;
}

export interface DashboardAnalytics {
    total_users: number;
    present_today: number;
    absent_today: number;
    attendance_rate: number;
    weekly_rate: number;
    monthly_rate: number;
    consistent_attendees: number;
    needs_followup: number;
    trends: AttendanceTrend[];
}

export interface AttendanceTrend {
    date: string;
    present: number;
    absent: number;
    rate: number;
}
```

### 8.2 API Service Layer

```typescript
// resources/js/services/api.ts
class ApiService {
    private baseURL = '/api';

    // Users
    async getUsers(params?: UserFilters): Promise<PaginatedResponse<ApiUser>> {
        return this.get('/users', params);
    }

    async createUser(data: CreateUserData): Promise<ApiUser> {
        return this.post('/users', data);
    }

    async updateUser(id: string, data: UpdateUserData): Promise<ApiUser> {
        return this.put(`/users/${id}`, data);
    }

    // Attendance
    async markAttendance(data: MarkAttendanceData): Promise<ApiAttendance> {
        return this.post('/attendance/mark', data);
    }

    async getTodayAttendance(): Promise<ApiAttendance[]> {
        return this.get('/attendance/today');
    }

    async getDashboardAnalytics(): Promise<DashboardAnalytics> {
        return this.get('/analytics/dashboard');
    }

    // Generic HTTP methods
    private async get<T>(endpoint: string, params?: any): Promise<T> {
        const url = new URL(this.baseURL + endpoint, window.location.origin);
        if (params) {
            Object.keys(params).forEach((key) => url.searchParams.append(key, params[key]));
        }

        const response = await fetch(url.toString(), {
            headers: this.getHeaders(),
        });

        return this.handleResponse<T>(response);
    }

    private async post<T>(endpoint: string, data: any): Promise<T> {
        const response = await fetch(this.baseURL + endpoint, {
            method: 'POST',
            headers: this.getHeaders(),
            body: JSON.stringify(data),
        });

        return this.handleResponse<T>(response);
    }

    private getHeaders(): HeadersInit {
        return {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        };
    }

    private async handleResponse<T>(response: Response): Promise<T> {
        if (!response.ok) {
            throw new Error(`API Error: ${response.status}`);
        }
        return response.json();
    }
}

export const apiService = new ApiService();
```

---

## 9. Performance Requirements

### 9.1 Database Optimization

```sql
-- Essential Indexes for Performance
CREATE INDEX CONCURRENTLY idx_users_search ON users USING gin(to_tsvector('arabic', name));
CREATE INDEX CONCURRENTLY idx_users_phone_partial ON users(phone) WHERE is_active = true;
CREATE INDEX CONCURRENTLY idx_attendance_date_status ON attendances(meeting_date, status);
CREATE INDEX CONCURRENTLY idx_attendance_user_recent ON attendances(user_id, meeting_date DESC);

-- Partitioning for Large Attendance Data
CREATE TABLE attendances_2024 PARTITION OF attendances
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE attendances_2025 PARTITION OF attendances
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
```

### 9.2 Caching Strategy

```php
// Laravel Cache Configuration
// config/cache.php
'stores' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
    ],
],

// Cache Implementation in Services
class AnalyticsService
{
    public function getDashboardStats(): array
    {
        return Cache::remember('dashboard_stats', 300, function () {
            return [
                'total_users' => User::active()->count(),
                'present_today' => Attendance::today()->present()->count(),
                'attendance_rate' => $this->calculateAttendanceRate(),
                'trends' => $this->getAttendanceTrends(),
            ];
        });
    }

    public function invalidateDashboardCache(): void
    {
        Cache::forget('dashboard_stats');
        Cache::tags(['analytics'])->flush();
    }
}
```

### 9.3 Queue Configuration

```php
// config/queue.php
'connections' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => env('REDIS_QUEUE', 'default'),
        'retry_after' => 90,
        'block_for' => null,
        'after_commit' => false,
    ],
],

// Queue Jobs Priority
'high' => [
    'attendance_marking',
    'qr_generation',
],
'normal' => [
    'birthday_notifications',
    'report_generation',
],
'low' => [
    'data_cleanup',
    'backup_creation',
],
```

### 9.4 API Response Optimization

```php
// API Resource Classes for Consistent Responses
class UserResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'phone' => $this->phone,
            'gender' => $this->gender,
            'college' => $this->college,
            'academic_year' => $this->academic_year,
            'attendance_rate' => $this->when($request->include_stats, $this->attendance_rate),
            'qr_token' => $this->when($request->include_qr, $this->qr_token),
        ];
    }
}

// Pagination for Large Datasets
class UserController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $users = User::query()
            ->when($request->search, fn($q) => $q->where('name', 'like', "%{$request->search}%"))
            ->when($request->year, fn($q) => $q->byYear($request->year))
            ->paginate(50);

        return UserResource::collection($users)->response();
    }
}
```

---

## 10. Security Considerations

### 10.1 Input Validation & Sanitization

```php
// Form Request Classes with Security Rules
class StoreUserRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'regex:/^[\p{Arabic}\s]+$/u'],
            'phone' => ['required', 'string', 'size:11', 'regex:/^01[0-9]{9}$/', 'unique:users'],
            'address' => ['required', 'string', 'max:500', new NoScriptTags],
            'facebook_url' => ['nullable', 'url', 'max:255', new SafeUrl],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'name' => strip_tags($this->name),
            'address' => strip_tags($this->address),
            'phone' => preg_replace('/[^0-9]/', '', $this->phone),
        ]);
    }
}

// Custom Validation Rules
class NoScriptTags implements Rule
{
    public function passes($attribute, $value): bool
    {
        return !preg_match('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', $value);
    }
}
```

### 10.2 Rate Limiting & Throttling

```php
// config/app.php - Custom Rate Limiters
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Facades\RateLimiter;

RateLimiter::for('attendance', function (Request $request) {
    return Limit::perMinute(120)->by($request->user()?->id ?: $request->ip());
});

RateLimiter::for('qr-scan', function (Request $request) {
    return Limit::perMinute(30)->by($request->ip());
});

RateLimiter::for('export', function (Request $request) {
    return Limit::perHour(10)->by($request->user()->id);
});
```

### 10.3 Data Protection & Privacy

```php
// Model Encryption for Sensitive Data
class User extends Model
{
    protected $casts = [
        'phone' => 'encrypted',
        'address' => 'encrypted',
        'facebook_url' => 'encrypted',
    ];

    // Audit Trail Implementation
    use \Spatie\Activitylog\Traits\LogsActivity;

    protected static $logAttributes = ['name', 'phone', 'college', 'academic_year'];
    protected static $logOnlyDirty = true;

    public function getDescriptionForEvent(string $eventName): string
    {
        return "User {$this->name} was {$eventName}";
    }
}

// GDPR Compliance - Data Export/Deletion
class GdprService
{
    public function exportUserData(User $user): array
    {
        return [
            'personal_data' => $user->only(['name', 'phone', 'address', 'birth_date']),
            'attendance_history' => $user->attendances()->get(),
            'notifications' => $user->notifications()->get(),
        ];
    }

    public function deleteUserData(User $user): void
    {
        // Anonymize instead of delete for data integrity
        $user->update([
            'name' => 'Deleted User',
            'phone' => null,
            'address' => null,
            'facebook_url' => null,
            'is_active' => false,
        ]);
    }
}
```

### 10.4 API Security Headers

```php
// Middleware for Security Headers
class SecurityHeaders
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Content-Security-Policy', "default-src 'self'");

        return $response;
    }
}
```

---

## 11. Implementation Timeline & Milestones

### Phase 1: Foundation Setup (Week 1-2)

- [ ] Laravel project setup with required packages
- [ ] Database schema creation and migrations
- [ ] Basic authentication system (Sanctum)
- [ ] Core models and relationships
- [ ] API route structure
- [ ] Basic frontend integration with Inertia.js

### Phase 2: Core Features (Week 3-4)

- [ ] User management CRUD operations
- [ ] Attendance marking system (manual & QR)
- [ ] Dashboard with basic analytics
- [ ] Search functionality integration
- [ ] Basic validation and error handling

### Phase 3: Advanced Features (Week 5-6)

- [ ] Reports generation and export
- [ ] Birthday management system
- [ ] WhatsApp integration
- [ ] Bulk operations
- [ ] Advanced analytics and trends

### Phase 4: Optimization & Security (Week 7)

- [ ] Performance optimization
- [ ] Security hardening
- [ ] Caching implementation
- [ ] Queue system setup
- [ ] Comprehensive testing

### Phase 5: Deployment & Go-Live (Week 8)

- [ ] Production environment setup
- [ ] Data migration and seeding
- [ ] User training and documentation
- [ ] Monitoring and logging setup
- [ ] Go-live and support

---

## 12. Testing Strategy

### 12.1 Unit Tests

```php
// tests/Unit/UserServiceTest.php
class UserServiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_user_with_valid_data(): void
    {
        $userData = [
            'name' => 'أحمد محمد',
            'phone' => '01234567890',
            'gender' => 'male',
            // ... other fields
        ];

        $user = app(UserService::class)->createUser($userData);

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals($userData['name'], $user->name);
        $this->assertNotNull($user->qr_token);
    }
}
```

### 12.2 Feature Tests

```php
// tests/Feature/AttendanceTest.php
class AttendanceTest extends TestCase
{
    public function test_can_mark_attendance_via_api(): void
    {
        $user = User::factory()->create();

        $response = $this->postJson('/api/attendance/mark', [
            'user_id' => $user->id,
            'status' => 'present',
            'method' => 'manual'
        ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('attendances', [
            'user_id' => $user->id,
            'status' => 'present'
        ]);
    }
}
```

---

## 13. Deployment Configuration

### 13.1 Environment Variables

```bash
# .env.production
APP_ENV=production
APP_DEBUG=false
APP_URL=https://attendance.arsanios.org

DB_CONNECTION=pgsql
DB_HOST=your-neon-host
DB_DATABASE=attendance_db
DB_USERNAME=your-username
DB_PASSWORD=your-password

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

QUEUE_CONNECTION=redis
CACHE_DRIVER=redis
SESSION_DRIVER=redis

TYPESENSE_HOST=localhost
TYPESENSE_PORT=8108
TYPESENSE_PROTOCOL=http
TYPESENSE_API_KEY=your-api-key

WHATSAPP_API_URL=https://api.whatsapp.com
WHATSAPP_TOKEN=your-token
```

### 13.2 Docker Configuration

```dockerfile
# Dockerfile
FROM php:8.2-fpm

RUN apt-get update && apt-get install -y \
    git curl libpng-dev libonig-dev libxml2-dev zip unzip \
    postgresql-client

RUN docker-php-ext-install pdo pdo_pgsql mbstring exif pcntl bcmath gd

COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

WORKDIR /var/www
COPY . .

RUN composer install --optimize-autoloader --no-dev
RUN php artisan config:cache
RUN php artisan route:cache
RUN php artisan view:cache

EXPOSE 9000
CMD ["php-fpm"]
```

This comprehensive technical specification provides a complete roadmap for implementing the Arsanios Youth Meeting Attendance Management System with Laravel backend. The document covers all aspects from database design to deployment, ensuring a robust, scalable, and secure implementation.
