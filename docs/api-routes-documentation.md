# API Routes Documentation

## Authentication Routes (Public)
- `POST /api/auth/login` - Admin login (email, password)
- `POST /api/auth/logout` - Admin logout (requires auth)
- `POST /api/auth/refresh` - Refresh token (requires auth)
- `GET /api/auth/user` - Get authenticated admin info (requires auth)

## Admin Management Routes (Protected)
- `GET /api/admins` - List all admins
- `POST /api/admins` - Create new admin
- `GET /api/admins/{id}` - Get admin details
- `PUT /api/admins/{id}` - Update admin
- `DELETE /api/admins/{id}` - Deactivate admin

## User Management Routes (Protected)
- `GET /api/users` - List all attendance users
- `POST /api/users` - Create new user
- `GET /api/users/{id}` - Get user details
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user
- `GET /api/users/{user}/qr` - Generate QR code for user
- `POST /api/users/bulk` - Bulk operations on users
- `GET /api/users/search` - Search users

## Attendance Management Routes (Protected)
- `GET /api/attendance` - List attendance records
- `POST /api/attendance/mark` - Mark attendance
- `GET /api/attendance/today` - Today's attendance
- `GET /api/attendance/user/{user}` - User attendance history
- `POST /api/attendance/bulk` - Bulk mark attendance
- `PUT /api/attendance/{attendance}` - Update attendance record
- `DELETE /api/attendance/{attendance}` - Delete attendance record

## Analytics & Dashboard Routes (Protected)
- `GET /api/analytics/dashboard` - Dashboard analytics
- `GET /api/analytics/trends` - Attendance trends
- `GET /api/analytics/performance` - Performance metrics

## Reports Routes (Protected)
- `GET /api/reports/weekly` - Weekly reports
- `GET /api/reports/monthly` - Monthly reports
- `GET /api/reports/yearly` - Yearly reports
- `POST /api/reports/custom` - Custom reports
- `GET /api/reports/export/{format}` - Export reports

## Birthday Management Routes (Protected)
- `GET /api/birthdays` - List birthdays
- `GET /api/birthdays/upcoming` - Upcoming birthdays
- `POST /api/birthdays/greetings` - Send birthday greetings
- `GET /api/birthdays/calendar` - Birthday calendar

## Settings & Configuration Routes (Protected)
- `GET /api/settings` - Get settings
- `PUT /api/settings` - Update settings
- `POST /api/settings/backup` - Backup system
- `POST /api/settings/restore` - Restore system

## Data Import/Export Routes (Protected)
- `POST /api/data/import/users` - Import users
- `POST /api/data/import/attendance` - Import attendance
- `GET /api/data/export/users` - Export users
- `GET /api/data/export/attendance` - Export attendance
- `GET /api/data/template/{type}` - Download templates

## Public Routes (No Authentication)
- `POST /api/attendance/qr-scan` - QR code attendance scanning
