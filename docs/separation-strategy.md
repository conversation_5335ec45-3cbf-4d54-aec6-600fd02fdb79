# Route Separation Strategy

## Architecture Decision: Hybrid Monolithic Approach

### Why This Approach?
1. **Simplified Deployment** - Single application to deploy
2. **Shared Authentication** - Unified admin system
3. **Development Efficiency** - Single codebase maintenance
4. **Resource Optimization** - Shared database and cache

## Route Separation Plan

### 1. API Routes (`/api/*`) - JSON Responses Only
**Purpose**: Serve data for mobile apps, external integrations, and AJAX requests
**Authentication**: Laravel Sanctum (Bearer tokens)
**Response Format**: JSON only
**CORS**: Enabled for external access

```php
// routes/api.php
Route::prefix('api')->group(function () {
    // All existing API routes remain here
    // Always return JSON responses
    // Use Sanctum authentication
});
```

### 2. Web Routes (`/*`) - React SPA
**Purpose**: Serve the React frontend application
**Authentication**: Laravel Session-based auth (for admins)
**Response Format**: Inertia.js pages or HTML
**CORS**: Not needed (same origin)

```php
// routes/web.php
Route::get('/', [WelcomeController::class, 'index']);

// React SPA routes (catch-all for React Router)
Route::get('/{any}', function () {
    return Inertia::render('App');
})->where('any', '^(?!api|health|api-docs).*$');
```

### 3. Admin Panel Routes (`/admin/*`) - Optional
**Purpose**: Separate admin interface if needed
**Authentication**: Laravel Session-based auth
**Response Format**: Blade templates or Inertia pages

## Authentication Strategy

### Dual Authentication System
1. **API Authentication (Sanctum)**
   - For mobile apps and external integrations
   - Token-based authentication
   - Stateless

2. **Web Authentication (Laravel Session)**
   - For React frontend
   - Session-based authentication
   - Stateful

### Implementation
```php
// config/auth.php
'guards' => [
    'web' => [
        'driver' => 'session',
        'provider' => 'admins',
    ],
    'api' => [
        'driver' => 'sanctum',
        'provider' => 'admins',
    ],
],
```

## File Structure Organization

### Backend (Laravel)
```
app/Http/Controllers/
├── Api/              # API controllers (JSON responses)
│   ├── AuthController.php
│   ├── UserController.php
│   └── AttendanceController.php
├── Web/              # Web controllers (Inertia responses)
│   ├── DashboardController.php
│   ├── UserController.php
│   └── AttendanceController.php
└── WelcomeController.php
```

### Frontend (React)
```
resources/js/
├── pages/            # Inertia.js pages
├── components/       # Reusable components
├── layouts/          # Page layouts
├── contexts/         # React contexts
├── hooks/            # Custom hooks
├── stores/           # State management
└── types/            # TypeScript types
```

## Route Middleware Strategy

### API Routes
- `throttle:api` - Rate limiting
- `auth:sanctum` - Token authentication
- `cors` - CORS headers

### Web Routes
- `web` - Session handling
- `auth` - Session authentication
- `verified` - Email verification

## Response Format Standards

### API Responses (JSON)
```json
{
    "success": true,
    "data": {...},
    "message": "Operation successful",
    "errors": null
}
```

### Web Responses (Inertia)
```php
return Inertia::render('Dashboard', [
    'users' => $users,
    'stats' => $stats
]);
```

## Development Workflow

### API Development
1. Create controllers in `app/Http/Controllers/Api/`
2. Define routes in `routes/api.php`
3. Test with Postman/Insomnia
4. Document in `docs/api-routes-documentation.md`

### Frontend Development
1. Create pages in `resources/js/pages/`
2. Define routes in `routes/web.php`
3. Use Inertia.js for data binding
4. Test in browser

## Deployment Configuration

### Single Application Deployment
1. Build React assets: `npm run build`
2. Deploy Laravel application
3. Configure web server to serve both API and web routes
4. Set up proper CORS for API routes

### Environment Variables
```env
# API Configuration
API_RATE_LIMIT=60
SANCTUM_STATEFUL_DOMAINS=localhost,127.0.0.1,arsanios.local

# Frontend Configuration
VITE_APP_NAME="Arsanios Attendance System"
VITE_API_URL="${APP_URL}/api"
```
