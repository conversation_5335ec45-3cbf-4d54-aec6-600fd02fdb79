# Complete Route Separation Documentation

## 🎯 Architecture Overview

The Arsanios Attendance System uses a **hybrid monolithic approach** with clear separation between API and frontend routes:

- **API Routes** (`/api/*`) - JSON responses for mobile apps and integrations
- **Web Routes** (`/*`) - React frontend served via Inertia.js
- **Shared Authentication** - Dual system (Sanctum + Laravel Session)

## 📡 API Routes (`/api/*`)

### Base Configuration
- **Base URL**: `/api/*`
- **Authentication**: Laravel Sanctum (Bearer tokens)
- **Response Format**: JSON only
- **Rate Limiting**: 60 requests per minute
- **CORS**: Enabled for external access

### Public Routes (No Authentication)
```
POST /api/auth/login              # Admin login
POST /api/attendance/qr-scan      # QR code scanning
```

### Protected Routes (Require Bearer Token)
```
# Authentication Management
POST /api/auth/logout             # Logout admin
POST /api/auth/refresh            # Refresh token
GET  /api/auth/user               # Get admin info

# Admin Management
GET    /api/admins                # List admins
POST   /api/admins                # Create admin
GET    /api/admins/{id}           # Get admin
PUT    /api/admins/{id}           # Update admin
DELETE /api/admins/{id}           # Delete admin

# User Management (Attendance Users)
GET    /api/users                 # List users
POST   /api/users                 # Create user
GET    /api/users/{id}            # Get user
PUT    /api/users/{id}            # Update user
DELETE /api/users/{id}            # Delete user
GET    /api/users/{id}/qr         # Generate QR code
POST   /api/users/bulk            # Bulk operations
GET    /api/users/search          # Search users

# Attendance Management
GET    /api/attendance            # List attendance
POST   /api/attendance/mark       # Mark attendance
GET    /api/attendance/today      # Today's attendance
GET    /api/attendance/user/{id}  # User history
POST   /api/attendance/bulk       # Bulk mark
PUT    /api/attendance/{id}       # Update record
DELETE /api/attendance/{id}       # Delete record

# Analytics & Reports
GET    /api/analytics/dashboard   # Dashboard data
GET    /api/analytics/trends      # Trends data
GET    /api/analytics/performance # Performance metrics
GET    /api/reports/weekly        # Weekly reports
GET    /api/reports/monthly       # Monthly reports
GET    /api/reports/yearly        # Yearly reports
POST   /api/reports/custom        # Custom reports
GET    /api/reports/export/{fmt}  # Export reports

# Birthday Management
GET    /api/birthdays             # List birthdays
GET    /api/birthdays/upcoming    # Upcoming birthdays
POST   /api/birthdays/greetings   # Send greetings
GET    /api/birthdays/calendar    # Birthday calendar

# Settings & Configuration
GET    /api/settings              # Get settings
PUT    /api/settings              # Update settings
POST   /api/settings/backup       # Backup system
POST   /api/settings/restore      # Restore system

# Data Import/Export
POST   /api/data/import/users     # Import users
POST   /api/data/import/attendance # Import attendance
GET    /api/data/export/users     # Export users
GET    /api/data/export/attendance # Export attendance
GET    /api/data/template/{type}  # Download templates
```

## 🖥️ Web Routes (`/*`)

### Base Configuration
- **Base URL**: `/*`
- **Authentication**: Laravel Session (for admins)
- **Response Format**: Inertia.js pages (React components)
- **State Management**: Zustand + React Context

### Public Routes
```
GET /                             # Welcome page
GET /health                       # Health check (JSON)
GET /api-docs                     # API documentation (JSON)
```

### Authentication Routes (Laravel Breeze)
```
GET  /login                       # Login page
POST /login                       # Process login
GET  /register                    # Registration page
POST /register                    # Process registration
GET  /forgot-password             # Password reset request
POST /forgot-password             # Send reset email
GET  /reset-password/{token}      # Password reset form
POST /reset-password              # Process password reset
POST /logout                      # Logout user
```

### Protected Routes (Require Session Auth)
```
GET /dashboard                    # Main dashboard
GET /attendance                   # Attendance management
GET /add-user                     # Add user form
GET /users                        # Users list (year 1)
GET /users/{year}                 # Users by academic year
GET /birthdays                    # Birthday management
GET /reports                      # Reports page
GET /app-settings                 # Application settings
```

## 🔐 Authentication Strategy

### Dual Authentication System

#### 1. API Authentication (Sanctum)
```php
// For API routes
'guards' => [
    'api' => [
        'driver' => 'sanctum',
        'provider' => 'admins',
    ],
],

// Usage
Route::middleware('auth:sanctum')->group(function () {
    // API routes
});
```

#### 2. Web Authentication (Session)
```php
// For web routes
'guards' => [
    'web' => [
        'driver' => 'session',
        'provider' => 'admins',
    ],
],

// Usage
Route::middleware(['auth', 'verified'])->group(function () {
    // Web routes
});
```

## 📁 File Organization

### Backend Controllers
```
app/Http/Controllers/
├── Api/                          # API controllers (JSON responses)
│   ├── AuthController.php        # API authentication
│   ├── AdminController.php       # Admin management API
│   ├── UserController.php        # User management API
│   ├── AttendanceController.php  # Attendance API
│   ├── AnalyticsController.php   # Analytics API
│   ├── ReportController.php      # Reports API
│   ├── BirthdayController.php    # Birthday API
│   ├── SettingController.php     # Settings API
│   └── DataController.php        # Import/Export API
├── Auth/                         # Laravel Breeze auth controllers
└── WelcomeController.php         # Welcome page controller
```

### Frontend Structure
```
resources/js/
├── pages/                        # Inertia.js pages
│   ├── auth/                     # Authentication pages
│   ├── dashboard.tsx             # Main dashboard
│   ├── attendance.tsx            # Attendance management
│   ├── users.tsx                 # User management
│   ├── birthdays.tsx             # Birthday management
│   ├── reports.tsx               # Reports page
│   └── settings/                 # Settings pages
├── components/                   # Reusable components
├── layouts/                      # Page layouts
├── contexts/                     # React contexts
├── hooks/                        # Custom hooks
├── stores/                       # Zustand stores
└── types/                        # TypeScript types
```

## 🚀 Development Workflow

### API Development
1. Create controller in `app/Http/Controllers/Api/`
2. Define routes in `routes/api.php`
3. Test with Postman/Insomnia
4. Always return JSON responses

### Frontend Development
1. Create page in `resources/js/pages/`
2. Define route in `routes/web.php`
3. Use Inertia.js for data binding
4. Test in browser

## 🌐 Deployment Configuration

### Single Application Deployment
1. Build React assets: `npm run build`
2. Deploy Laravel application
3. Configure web server (Nginx/Apache)
4. Set environment variables

### Environment Variables
```env
# API Configuration
SANCTUM_STATEFUL_DOMAINS=localhost,127.0.0.1,arsanios.local
API_RATE_LIMIT=60

# Frontend Configuration
VITE_APP_NAME="Arsanios Attendance System"
VITE_API_URL="${APP_URL}/api"
```

This separation provides clear boundaries while maintaining the benefits of a monolithic architecture.
