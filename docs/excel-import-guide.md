# Excel Import Guide for Arsanios Attendance System

## Excel File Structure

### Required Sheets
Your Excel file should contain **4 sheets**, one for each academic year:
- **Sheet 1**: "السنة الأولى" or "Year 1" 
- **Sheet 2**: "السنة الثانية" or "Year 2"
- **Sheet 3**: "السنة الثالثة" or "Year 3" 
- **Sheet 4**: "السنة الرابعة" or "Year 4"

### Required Columns (Same for all sheets)

| **Column** | **Arabic Name** | **Type** | **Required** | **Example** |
|------------|-----------------|----------|--------------|-------------|
| `name` | الاسم | Text | ✅ Yes | أحمد محمد علي |
| `phone` | رقم الهاتف | Text (11 digits) | ✅ Yes | 01234567890 |
| `gender` | النوع | Text | ✅ Yes | male / female |
| `birth_date` | تاريخ الميلاد | Date | ✅ Yes | 2000-05-15 |
| `address` | العنوان | Text | ❌ No | المنيا - مصر |
| `facebook_url` | رابط الفيسبوك | URL | ❌ No | https://facebook.com/username |
| `college` | الكلية | Text | ✅ Yes | كلية العلوم |
| `academic_year` | السنة الدراسية | Number | ✅ Yes | 1, 2, 3, or 4 |
| `department` | القسم | Text | ❌ No | رياضيات |
| `notes` | ملاحظات | Text | ❌ No | طالب متميز |

### Data Validation Rules

#### Phone Number
- **Format**: Must be exactly 11 digits
- **Pattern**: Must start with `01` (Egyptian mobile)
- **Example**: `01234567890`

#### Gender
- **Values**: `male` or `female` (English only)
- **Arabic equivalent**: ذكر = male, أنثى = female

#### Birth Date
- **Format**: `YYYY-MM-DD` (ISO format)
- **Example**: `2000-05-15` for May 15, 2000
- **Validation**: Must be before today's date

#### Academic Year
- **Values**: 1, 2, 3, or 4 (numbers only)
- **Note**: Should match the sheet name

#### College Examples
- `كلية العلوم` (Faculty of Science)
- `كلية التربية` (Faculty of Education)
- `كلية الهندسة` (Faculty of Engineering)
- `كلية الطب` (Faculty of Medicine)

#### Department Examples
- **Science**: رياضيات، فيزياء، كيمياء، أحياء، جيولوجيا
- **Education**: لغة عربية، لغة إنجليزية، تاريخ، جغرافيا، علوم

## Excel Template Structure

### Sheet 1: السنة الأولى (Year 1)
```
A1: name          B1: phone        C1: gender       D1: birth_date
A2: أحمد محمد علي  B2: 01234567890  C2: male         D2: 2000-05-15
A3: فاطمة أحمد     B3: 01098765432  C3: female       D3: 2001-03-20
...
```

### Sheet 2: السنة الثانية (Year 2)
```
A1: name          B1: phone        C1: gender       D1: birth_date
A2: محمد سامح     B2: 01156789012  C2: male         D2: 1999-12-10
A3: مريم جورج     B3: 01287654321  C3: female       D3: 2000-08-25
...
```

### Sheet 3: السنة الثالثة (Year 3)
```
A1: name          B1: phone        C1: gender       D1: birth_date
A2: يوسف عادل     B2: 01345678901  C2: male         D2: 1998-11-30
A3: نورا صلاح     B3: 01456789012  C3: female       D3: 1999-07-18
...
```

### Sheet 4: السنة الرابعة (Year 4)
```
A1: name          B1: phone        C1: gender       D1: birth_date
A2: كريم محمد     B2: 01567890123  C2: male         D2: 1997-09-05
A3: سارة عبد الرحمن B3: 01678901234  C3: female       D3: 1998-02-14
...
```

## Common Mistakes to Avoid

### ❌ Wrong Phone Format
- Don't use: `+201234567890`, `(*************`, `************`
- ✅ Use: `01234567890`

### ❌ Wrong Gender Values
- Don't use: `ذكر`, `أنثى`, `M`, `F`, `Male`, `Female`
- ✅ Use: `male`, `female`

### ❌ Wrong Date Format
- Don't use: `15/5/2000`, `15-May-2000`, `2000/5/15`
- ✅ Use: `2000-05-15`

### ❌ Missing Required Fields
- All required fields must have values
- Empty cells in required columns will cause import errors

### ❌ Wrong Academic Year
- Make sure academic_year column matches the sheet
- Sheet 1 should have academic_year = 1, etc.

## File Naming Convention
- **Recommended**: `arsanios_users_2024.xlsx`
- **Format**: Excel (.xlsx) or CSV (.csv)
- **Encoding**: UTF-8 for Arabic text support

## Import Process
1. Prepare Excel file with 4 sheets
2. Fill data according to the template
3. Save as .xlsx format
4. Upload through the admin panel or use the seeder
5. System will validate and import data
6. QR codes will be automatically generated for each user
