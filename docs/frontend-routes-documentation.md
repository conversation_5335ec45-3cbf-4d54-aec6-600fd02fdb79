# Frontend Routes Documentation

## Public Routes (No Authentication)
- `GET /` - Welcome page (WelcomeController)
- `GET /health` - System health check (JSON response)
- `GET /api-docs` - API documentation (JSON response)

## Authentication Routes (<PERSON><PERSON> Auth)
- `GET /login` - Login page (Inertia: auth/login)
- `POST /login` - Process login
- `GET /register` - Registration page (Inertia: auth/register)
- `POST /register` - Process registration
- `GET /forgot-password` - Password reset request
- `POST /forgot-password` - Send reset email
- `GET /reset-password/{token}` - Password reset form
- `POST /reset-password` - Process password reset
- `POST /logout` - Logout user

## Protected Routes (Requires Laravel Auth)
- `GET /dashboard` - Main dashboard (Inertia: dashboard)
- `GET /attendance` - Attendance management (Inertia: attendance)
- `GET /add-user` - Add new user form (Inertia: add-user)
- `GET /users` - Users list (year 1) (Inertia: users)
- `GET /users/{year}` - Users by academic year (Inertia: users)
- `GET /birthdays` - Birthday management (Inertia: birthdays)
- `GET /reports` - Reports page (Inertia: reports)
- `GET /app-settings` - Application settings (Inertia: app-settings)

## React Components Structure
```
resources/js/pages/
├── auth/
│   ├── login.tsx
│   └── register.tsx
├── dashboard.tsx
├── attendance.tsx
├── add-user.tsx
├── users.tsx
├── birthdays.tsx
├── reports.tsx
├── settings/
│   └── app-settings.tsx
└── welcome.tsx
```

## Layouts
- `AppLayout` - Main application layout
- `AuthLayout` - Authentication pages layout
- `AppDashboardLayout` - Dashboard specific layout

## Context Providers
- `AuthProvider` - Authentication context
- `RTLProvider` - Right-to-left language support

## State Management
- Zustand store (`app-store.ts`) for global state
- React Hook Form for form management
