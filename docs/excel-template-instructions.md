# Excel Template Instructions for Your Existing Data

## 🎯 Converting Your Existing Excel File

Since you mentioned you already have an Excel file with 4 sheets, here's how to prepare it for import:

### Step 1: Verify Your Excel Structure

Your Excel file should have **4 sheets** with these names:
- Sheet 1: السنة الأولى (or Year 1)
- Sheet 2: السنة الثانية (or Year 2)  
- Sheet 3: السنة الثالثة (or Year 3)
- Sheet 4: السنة الرابعة (or Year 4)

### Step 2: Update Column Headers

Make sure **each sheet** has these exact column headers in **Row 1**:

| Column A | Column B | Column C | Column D | Column E | Column F | Column G | Column H | Column I |
|----------|----------|----------|----------|----------|----------|----------|----------|----------|
| `name` | `phone` | `gender` | `birth_date` | `address` | `facebook_url` | `college` | `department` | `notes` |

### Step 3: Data Format Requirements

#### Column A: name (الاسم)
- **Format**: Text
- **Example**: أحمد محمد علي
- **Required**: ✅ Yes

#### Column B: phone (رقم الهاتف)
- **Format**: Text (exactly 11 digits)
- **Pattern**: Must start with `01`
- **Example**: `01234567890`
- **Required**: ✅ Yes

#### Column C: gender (النوع)
- **Format**: Text
- **Values**: `male` or `female` (English only)
- **Example**: `male` for ذكر, `female` for أنثى
- **Required**: ✅ Yes

#### Column D: birth_date (تاريخ الميلاد)
- **Format**: Date or Text
- **Pattern**: YYYY-MM-DD
- **Example**: `2000-05-15` for May 15, 2000
- **Required**: ✅ Yes

#### Column E: address (العنوان)
- **Format**: Text
- **Example**: المنيا - مصر
- **Required**: ❌ No (defaults to "المنيا - مصر")

#### Column F: facebook_url (رابط الفيسبوك)
- **Format**: URL or Text
- **Example**: https://facebook.com/username
- **Required**: ❌ No

#### Column G: college (الكلية)
- **Format**: Text
- **Example**: كلية العلوم، كلية التربية
- **Required**: ✅ Yes

#### Column H: department (القسم)
- **Format**: Text
- **Example**: رياضيات، فيزياء، لغة عربية
- **Required**: ❌ No

#### Column I: notes (ملاحظات)
- **Format**: Text
- **Example**: طالب متميز، نشط في الأنشطة
- **Required**: ❌ No

### Step 4: Data Conversion Examples

#### Converting Gender Values
```
If your data has:     Change to:
ذكر                  male
أنثى                 female
M                    male
F                    female
Male                 male
Female               female
```

#### Converting Phone Numbers
```
If your data has:           Change to:
+************              01234567890
(*************             01234567890
************               01234567890
************               01234567890
```

#### Converting Dates
```
If your data has:     Change to:
15/5/2000            2000-05-15
15-May-2000          2000-05-15
May 15, 2000         2000-05-15
2000/5/15            2000-05-15
```

### Step 5: Academic Year Guidelines

#### Year 1 Students (السنة الأولى)
- **Birth Year**: ~2003
- **Age**: ~21 years old
- **Status**: First year university students

#### Year 2 Students (السنة الثانية)
- **Birth Year**: ~2002
- **Age**: ~22 years old
- **Status**: Second year university students

#### Year 3 Students (السنة الثالثة)
- **Birth Year**: ~2001
- **Age**: ~23 years old
- **Status**: Third year university students

#### Year 4 Students (السنة الرابعة)
- **Birth Year**: ~2000
- **Age**: ~24 years old
- **Status**: Final year university students

### Step 6: Save and Import

1. **Save your Excel file** as `arsanios_users.xlsx`
2. **Place it** in the folder: `storage/app/imports/arsanios_users.xlsx`
3. **Run the import command**:
   ```bash
   php artisan db:seed --class=UsersFromExcelSeeder
   ```

### Step 7: Verify Import

After import, check the results:
```bash
# Check users by year
php artisan tinker --execute="
for(\$year = 1; \$year <= 4; \$year++) {
    \$count = App\Models\User::where('academic_year', \$year)->count();
    echo 'Year ' . \$year . ': ' . \$count . ' users' . PHP_EOL;
}
"
```

## 🔧 Quick Conversion Tips

### Excel Formula for Phone Cleanup
If your phone numbers have different formats, use this Excel formula:
```excel
=RIGHT("0000000000"&SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(A2,"+2",""),"-","")," ",""),11)
```

### Excel Formula for Date Conversion
If your dates need conversion:
```excel
=TEXT(A2,"YYYY-MM-DD")
```

### Excel Formula for Gender Conversion
```excel
=IF(OR(A2="ذكر",A2="M",A2="Male"),"male",IF(OR(A2="أنثى",A2="F",A2="Female"),"female",""))
```

## ⚠️ Common Issues

### Issue: "Invalid phone number"
- Check that phone numbers are exactly 11 digits
- Ensure they start with `01`
- Remove any spaces, dashes, or country codes

### Issue: "Invalid gender value"
- Use only `male` or `female`
- Check for extra spaces or different spellings

### Issue: "Invalid date format"
- Use YYYY-MM-DD format
- Ensure dates are valid (not future dates)

### Issue: "Sheet not found"
- Verify you have exactly 4 sheets
- Check sheet names match expected format

## 📞 Support

If you need help converting your existing Excel file:
1. Share the current column structure
2. Provide sample data format
3. Mention any specific formatting issues

The system is designed to handle Arabic text properly and will generate QR codes automatically for each imported user.
