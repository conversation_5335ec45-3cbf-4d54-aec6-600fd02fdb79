# Product Requirements Document (PRD)

## Project Title:

**"Arsanios Youth Meeting Attendance Management System"**

## Project Description:

A web-based attendance management system designed for "اجتماع الأنبا أرسانيـوس لشباب كليتي علوم وتربية" to manage and monitor member attendance, birthdays, reports, and data efficiently using QR codes and automated workflows.

---

## Goals & Objectives:

1. Streamline attendance tracking for youth meetings.
2. Automate QR code-based check-ins.
3. Provide real-time dashboards with statistics and charts.
4. Simplify user management and batch operations.
5. Generate reports in PDF/Excel formats.
6. Manage and send automated birthday greetings via WhatsApp.
7. Enable data import/export via Excel templates.

---

## Target Users:

* Church Youth Meeting Organizers
* Reception Volunteers
* Follow-up Committee Members

---

## Key Features & Modules:

### 1. Dashboard (/dashboard)

* Total Members
* Present Today
* Absent Today
* Regular Attendees
* Needs Follow-up
* Weekly Attendance Rate
* Attendance Charts (Trends, Heatmaps)

### 2. Attendance Management (/attendance)

* Tabs for each academic year (1st to 4th)
* Fast Search (Typesense)
* Advanced Filters (College, Year, Follow-up Status)
* QR Code Scanner Integration
* Manual Attendance Marking

### 3. Add New User (/add-user)

* Personal Info: Full Name, Phone, Gender, Birthdate, Address, Facebook (optional)
* Academic Info: College, Academic Year, Department
* First Attendance Date (Default to today)
* QR Token auto-generated upon save

### 4. User Management (/users/\[year])

* Table per Year (1st, 2nd, 3rd, 4th)
* Search & Filters (Typesense Powered)
* Batch Actions (Messaging, Follow-up Tagging)

### 5. Reports (/reports)

* Weekly/Monthly/Yearly Reports
* Filter by Year/College
* Export PDF/Excel

### 6. Birthdays (/birthdays)

* List View & Calendar View
* Filters by Month
* WhatsApp Integration for Auto-greetings

### 7. Settings (/settings)

* UI Language & Direction (RTL/LTR)
* Attendance Settings (Auto-absence time, Cut-off time)
* Birthday Notifications (Frequency, Template Message)
* WhatsApp Settings (Greeting Templates with variables)
* Data Backup & Import/Export

---

## Database Design (ERD)

### 1. Users Table

| Field                   | Type                                | Notes                |
| ----------------------- | ----------------------------------- | -------------------- |
| id                      | BIGINT (auto-increment)             | Primary Key          |
| name                    | VARCHAR(255)                        |                      |
| phone                   | VARCHAR(20)                         |                      |
| gender                  | ENUM('male','female')               |                      |
| birth\_date             | DATE                                |                      |
| address                 | TEXT                                |                      |
| facebook\_url           | VARCHAR(255)                        | Optional             |
| college                 | ENUM('Science','Education','Other') |                      |
| academic\_year          | TINYINT                             | 1-4                  |
| department              | VARCHAR(100)                        |                      |
| first\_attendance\_date | DATE                                | Default current date |
| qr\_token               | VARCHAR(100)                        | Unique QR Token      |
| created\_at             | TIMESTAMP                           |                      |
| updated\_at             | TIMESTAMP                           |                      |

### 2. Attendances Table

| Field         | Type                     | Notes                  |
| ------------- | ------------------------ | ---------------------- |
| id            | BIGINT (auto-increment)  | Primary Key            |
| user\_id      | BIGINT                   | Foreign Key (users.id) |
| meeting\_date | DATE                     | Date of the Meeting    |
| status        | ENUM('present','absent') |                        |
| created\_at   | TIMESTAMP                |                        |

### 3. Settings Table

| Field       | Type                    | Notes       |
| ----------- | ----------------------- | ----------- |
| id          | BIGINT (auto-increment) | Primary Key |
| key         | VARCHAR(100)            | Setting Key |
| value       | TEXT                    | JSON Value  |
| created\_at | TIMESTAMP               |             |
| updated\_at | TIMESTAMP               |             |

### 4. Notifications Table

| Field    | Type                        | Notes                  |
| -------- | --------------------------- | ---------------------- |
| id       | BIGINT (auto-increment)     | Primary Key            |
| user\_id | BIGINT                      | Foreign Key (users.id) |
| type     | ENUM('birthday','reminder') |                        |
| message  | TEXT                        |                        |
| sent\_at | TIMESTAMP                   |                        |

### 5. Import Logs Table (Optional)

| Field        | Type                    | Notes              |
| ------------ | ----------------------- | ------------------ |
| id           | BIGINT (auto-increment) | Primary Key        |
| file\_name   | VARCHAR(255)            | Imported File Name |
| imported\_at | TIMESTAMP               |                    |

---

## QR Code Generation Logic

* QR Token Format: `ARSANIOS-USER-{ID}` or UUID for extra security.
* QR Token stored in `users.qr_token`.
* Frontend generates QR on-the-fly using `qrcode.react` component.
* QR Code Scanning triggers API call to `/api/attendance/mark` with the `qr_token`.

---

## API Endpoints (Detailed)

### Authentication (Sanctum)

* POST `/api/login` → Login and issue Sanctum token.
* POST `/api/logout` → Revoke user token.

### Users

* GET `/api/users` → List all users (with filters, pagination).
* POST `/api/users` → Add new user.
* GET `/api/users/{id}` → Get user details.
* PUT `/api/users/{id}` → Update user information.
* DELETE `/api/users/{id}` → Delete user.

### Attendance

* POST `/api/attendance/mark` → Mark attendance via QR Token `{ qr_token: "..." }`.
* GET `/api/attendance` → Get attendance list for a date (`?date=YYYY-MM-DD`).
* GET `/api/attendance/user/{id}` → Get user's full attendance history.

### Reports

* GET `/api/reports/weekly` → Weekly report summary.
* GET `/api/reports/monthly` → Monthly report summary.
* GET `/api/reports/yearly` → Yearly report summary.
* GET `/api/reports/export/pdf` → Export report as PDF.
* GET `/api/reports/export/excel` → Export report as Excel.

### Birthdays

* GET `/api/birthdays` → List birthdays (`?month=MM`).
* POST `/api/birthdays/send-greetings` → Send WhatsApp greetings to users.

### Settings

* GET `/api/settings` → Get all system settings.
* PUT `/api/settings` → Update settings (JSON payload).

### Data Import/Export

* POST `/api/data/import` → Upload Excel file and import users.
* GET `/api/data/export` → Export current user data as Excel.

---

## Search Engine Integration

* Typesense collection: `users`
* Sync Laravel Models with Typesense upon create/update.
* Instant search & filters on frontend (Next.js).

---

## Non-Functional Requirements

* Fast QR Scanning & Real-time attendance marking.
* Dashboard loads under 2 seconds.
* Data import supports up to 1000 users per upload.
* System supports RTL (Arabic) and LTR (English).
* Mobile-friendly UI for QR scanning.

---

## Deployment Plan

* Backend: Laravel API deployed on Coolify/DigitalOcean.
* Frontend: Next.js deployed on Vercel/Coolify.
* Typesense: Self-hosted in Docker Compose.
* PostgreSQL: Managed DB or Docker.
* Cloudflare: SSL, CDN, and Caching.

---

## Optional Future Features

* Realtime Notifications (Laravel Echo + Pusher)
* Attendance Gamification (Badges, Rankings)
* Multi-Event Support (Retreats, Conferences)
* Role-based Admin Panel (Super Admin, Receptionist, Follow-up Team)

---

## Timeline Estimate

| Phase                  | Duration |
| ---------------------- | -------- |
| Project Setup          | 2 Days   |
| Auth & User Management | 3 Days   |
| Attendance System      | 4 Days   |
| Dashboard & Reports    | 3 Days   |
| Birthdays & WhatsApp   | 3 Days   |
| Settings & Data Import | 2 Days   |
| Testing & QA           | 3 Days   |
| Deployment & Go Live   | 1 Day    |

---

## Team

* Product Owner: Abanoub Nashaat
* Backend Developer: You (Laravel API)
* Frontend Developer: You (Next.js + Tailwind)
* DevOps: You (Coolify, Docker)

---

## Let's Build It! 🚀
