<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AdminController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\AttendanceController;
use App\Http\Controllers\Api\AnalyticsController;
use App\Http\Controllers\Api\ReportController;
use App\Http\Controllers\Api\BirthdayController;
use App\Http\Controllers\Api\SettingController;
use App\Http\Controllers\Api\DataController;

/*
|--------------------------------------------------------------------------
| API Routes - JSON Responses Only
|--------------------------------------------------------------------------
|
| These routes serve the REST API for mobile apps, external integrations,
| and AJAX requests. All routes return JSON responses and use Laravel
| Sanctum for token-based authentication.
|
| Base URL: /api/*
| Authentication: Bearer tokens (Laravel Sanctum)
| Response Format: JSON only
|
*/

/*
|--------------------------------------------------------------------------
| Public API Routes (No Authentication Required)
|--------------------------------------------------------------------------
*/

// Authentication Routes
Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');
    Route::post('refresh', [AuthController::class, 'refresh'])->middleware('auth:sanctum');
    Route::get('user', [AuthController::class, 'user'])->middleware('auth:sanctum');
});

// Public QR Code Scanning Route
Route::post('attendance/qr-scan', [AttendanceController::class, 'scanQr']);


/*
|--------------------------------------------------------------------------
| Protected API Routes (Require Authentication)
|--------------------------------------------------------------------------
|
| All routes below require valid Sanctum token authentication
| Header: Authorization: Bearer {token}
|
*/

Route::middleware('auth:sanctum')->group(function () {

    // Admin Management Routes
    Route::apiResource('admins', AdminController::class);

    // User Management Routes (Attendance Users)
    Route::apiResource('users', UserController::class);
    Route::get('users/{user}/qr', [UserController::class, 'generateQr']);
    Route::post('users/bulk', [UserController::class, 'bulkOperations']);
    Route::get('users/search', [UserController::class, 'search']);

    // Attendance Management Routes
    Route::prefix('attendance')->group(function () {
        Route::get('/', [AttendanceController::class, 'index']);
        Route::post('mark', [AttendanceController::class, 'mark']);
        Route::get('today', [AttendanceController::class, 'today']);
        Route::get('user/{user}', [AttendanceController::class, 'userHistory']);
        Route::post('bulk', [AttendanceController::class, 'bulkMark']);
        Route::put('{attendance}', [AttendanceController::class, 'update']);
        Route::delete('{attendance}', [AttendanceController::class, 'destroy']);
    });

    // Analytics & Dashboard Routes
    Route::prefix('analytics')->group(function () {
        Route::get('dashboard', [AnalyticsController::class, 'dashboard']);
        Route::get('trends', [AnalyticsController::class, 'trends']);
        Route::get('performance', [AnalyticsController::class, 'performance']);
    });

    // Reports Routes
    Route::prefix('reports')->group(function () {
        Route::get('weekly', [ReportController::class, 'weekly']);
        Route::get('monthly', [ReportController::class, 'monthly']);
        Route::get('yearly', [ReportController::class, 'yearly']);
        Route::post('custom', [ReportController::class, 'custom']);
        Route::get('export/{format}', [ReportController::class, 'export']);
    });

    // Birthday Management Routes
    Route::prefix('birthdays')->group(function () {
        Route::get('/', [BirthdayController::class, 'index']);
        Route::get('upcoming', [BirthdayController::class, 'upcoming']);
        Route::post('greetings', [BirthdayController::class, 'sendGreetings']);
        Route::get('calendar', [BirthdayController::class, 'calendar']);
    });

    // Settings & Configuration Routes
    Route::prefix('settings')->group(function () {
        Route::get('/', [SettingController::class, 'index']);
        Route::put('/', [SettingController::class, 'update']);
        Route::post('backup', [SettingController::class, 'backup']);
        Route::post('restore', [SettingController::class, 'restore']);
    });

    // Data Import/Export Routes
    Route::prefix('data')->group(function () {
        Route::post('import/users', [DataController::class, 'importUsers']);
        Route::post('import/attendance', [DataController::class, 'importAttendance']);
        Route::get('export/users', [DataController::class, 'exportUsers']);
        Route::get('export/attendance', [DataController::class, 'exportAttendance']);
        Route::get('template/{type}', [DataController::class, 'downloadTemplate']);
    });
});


