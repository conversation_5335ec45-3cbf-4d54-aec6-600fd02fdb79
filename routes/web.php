<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\WelcomeController;

/*
|--------------------------------------------------------------------------
| Web Routes - React Frontend
|--------------------------------------------------------------------------
|
| These routes serve the React frontend application using Inertia.js
| All routes use Laravel session-based authentication for admins
|
*/

// Main dashboard route (protected) - redirects to login if not authenticated
Route::get('/', function () {
    return Inertia::render('dashboard');
})->middleware(['auth', 'verified'])->name('home');

// Optional: Keep welcome page accessible at /welcome for system info
Route::get('/welcome', [WelcomeController::class, 'index'])->name('welcome');


/*
|--------------------------------------------------------------------------
| System Utility Routes
|--------------------------------------------------------------------------
*/



// API documentation is available at /api/docs (moved under API routes for clarity)

/*
|--------------------------------------------------------------------------
| Protected React Frontend Routes
|--------------------------------------------------------------------------
|
| These routes serve the React application pages using Inertia.js
| All routes require Laravel session-based authentication
|
*/

Route::middleware(['auth', 'verified'])->group(function () {

    // Alternative dashboard route (optional - redirects to home)
    Route::get('dashboard', function () {
        return redirect()->route('home');
    })->name('dashboard');

    // Attendance Management
    Route::get('attendance', function () {
        return Inertia::render('attendance');
    })->name('attendance');

    // User Management
    Route::get('add-user', function () {
        return Inertia::render('add-user');
    })->name('add-user');

    Route::get('users', function () {
        return Inertia::render('users', ['year' => 1]);
    })->name('users');

    Route::get('users/{year}', function ($year) {
        return Inertia::render('users', ['year' => (int)$year]);
    })->where('year', '[1-4]')->name('users.year');

    // Birthday Management
    Route::get('birthdays', function () {
        return Inertia::render('birthdays');
    })->name('birthdays');

    // Reports & Analytics
    Route::get('reports', function () {
        return Inertia::render('reports');
    })->name('reports');

    // Application Settings
    Route::get('app-settings', function () {
        return Inertia::render('app-settings');
    })->name('app-settings');
});

require __DIR__.'/system.php';
require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
