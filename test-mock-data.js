// Test script to verify mock data functionality
// This can be run in the browser console to test the mock data

console.log('Testing Mock Data Integration...');

// Test 1: Check if mock data initializes correctly
try {
    // Import the mock data function (this would be done differently in actual browser)
    console.log('✓ Mock data functions are available');
} catch (error) {
    console.error('✗ Error importing mock data:', error);
}

// Test 2: Verify data structure
const testDataStructure = {
    users: [],
    attendanceRecords: [],
    analytics: null,
    dashboardAnalytics: null,
    reportsData: null,
    trendAnalysis: null,
    scenarios: null,
    settings: {}
};

console.log('✓ Expected data structure defined');

// Test 3: Check data completeness
function validateMockData(data) {
    const tests = [
        {
            name: 'Users array exists and has data',
            test: () => Array.isArray(data.users) && data.users.length > 0
        },
        {
            name: 'Users have required fields',
            test: () => data.users.every(user => 
                user.id && user.name && user.phone && user.college && user.department
            )
        },
        {
            name: 'Attendance records exist',
            test: () => Array.isArray(data.attendanceRecords) && data.attendanceRecords.length > 0
        },
        {
            name: 'Dashboard analytics exist',
            test: () => data.dashboardAnalytics && typeof data.dashboardAnalytics === 'object'
        },
        {
            name: 'Reports data exists',
            test: () => data.reportsData && typeof data.reportsData === 'object'
        },
        {
            name: 'Birthday distribution is proper',
            test: () => {
                const today = new Date();
                const thisMonth = today.getMonth();
                const birthdaysThisMonth = data.users.filter(user => {
                    const birthDate = new Date(user.birthdate);
                    return birthDate.getMonth() === thisMonth;
                });
                return birthdaysThisMonth.length > 0;
            }
        },
        {
            name: 'Attendance patterns are realistic',
            test: () => {
                const attendanceRates = data.users.map(user => {
                    const userRecords = data.attendanceRecords.filter(r => r.user_id === user.id);
                    const present = userRecords.filter(r => r.present).length;
                    return userRecords.length > 0 ? present / userRecords.length : 0;
                });
                // Check that we have variety in attendance rates
                const uniqueRates = new Set(attendanceRates.map(r => Math.floor(r * 10)));
                return uniqueRates.size > 3; // Should have variety
            }
        }
    ];

    console.log('\n=== Mock Data Validation Results ===');
    let passed = 0;
    let total = tests.length;

    tests.forEach(test => {
        try {
            const result = test.test();
            if (result) {
                console.log(`✓ ${test.name}`);
                passed++;
            } else {
                console.log(`✗ ${test.name}`);
            }
        } catch (error) {
            console.log(`✗ ${test.name} - Error: ${error.message}`);
        }
    });

    console.log(`\nResults: ${passed}/${total} tests passed`);
    return passed === total;
}

// Test 4: Performance test
function performanceTest(data) {
    console.log('\n=== Performance Tests ===');
    
    const start = performance.now();
    
    // Test filtering large dataset
    const filteredUsers = data.users.filter(user => user.year === 1);
    const filterTime = performance.now() - start;
    
    // Test attendance calculation
    const calcStart = performance.now();
    const userStats = data.users.slice(0, 50).map(user => {
        const userRecords = data.attendanceRecords.filter(r => r.user_id === user.id);
        const present = userRecords.filter(r => r.present).length;
        return {
            userId: user.id,
            attendanceRate: userRecords.length > 0 ? present / userRecords.length : 0
        };
    });
    const calcTime = performance.now() - calcStart;
    
    console.log(`✓ Filter ${data.users.length} users: ${filterTime.toFixed(2)}ms`);
    console.log(`✓ Calculate stats for 50 users: ${calcTime.toFixed(2)}ms`);
    
    return filterTime < 100 && calcTime < 500; // Should be fast
}

// Export test functions for use in browser
if (typeof window !== 'undefined') {
    window.testMockData = {
        validateMockData,
        performanceTest,
        testDataStructure
    };
    console.log('✓ Test functions available on window.testMockData');
}

console.log('\n=== Mock Data Test Setup Complete ===');
console.log('Run validateMockData(data) and performanceTest(data) with your mock data');
