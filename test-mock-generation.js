// Test script to verify mock data generation
// Run this in Node.js to test the mock data generation

// Mock the date-fns functions for testing
const mockDateFns = {
    format: (date, formatStr) => {
        if (formatStr === 'yyyy-MM-dd') {
            return date.toISOString().split('T')[0];
        }
        return date.toISOString();
    },
    subDays: (date, days) => {
        const result = new Date(date);
        result.setDate(result.getDate() - days);
        return result;
    },
    addDays: (date, days) => {
        const result = new Date(date);
        result.setDate(result.getDate() + days);
        return result;
    }
};

// Test data generation
function testMockDataGeneration() {
    console.log('Testing mock data generation...');
    
    // Test basic user generation
    const testUsers = [];
    for (let i = 0; i < 10; i++) {
        const user = {
            id: `user_${i + 1}`,
            name: `Test User ${i + 1}`,
            phone: `0101234567${i}`,
            gender: i % 2 === 0 ? 'male' : 'female',
            year: (Math.floor(Math.random() * 4) + 1),
            college: 'Test College',
            department: 'Test Department',
            birthdate: mockDateFns.format(new Date(2000 + i, i % 12, (i % 28) + 1), 'yyyy-MM-dd'),
            address: 'Test Address',
            facebook_url: '',
            first_attendance_date: mockDateFns.format(mockDateFns.subDays(new Date(), 100), 'yyyy-MM-dd'),
            qr_code: `QR_${i + 1}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
        };
        testUsers.push(user);
    }
    
    console.log('Generated test users:', testUsers.length);
    console.log('Year distribution:', testUsers.reduce((acc, user) => {
        acc[user.year] = (acc[user.year] || 0) + 1;
        return acc;
    }, {}));
    
    // Test year filtering
    const year1Users = testUsers.filter(user => user.year === 1);
    const year2Users = testUsers.filter(user => user.year === 2);
    const year3Users = testUsers.filter(user => user.year === 3);
    const year4Users = testUsers.filter(user => user.year === 4);
    
    console.log('Year filtering results:', {
        year1: year1Users.length,
        year2: year2Users.length,
        year3: year3Users.length,
        year4: year4Users.length
    });
    
    return testUsers;
}

// Test the function
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testMockDataGeneration };
} else {
    // Browser environment
    window.testMockDataGeneration = testMockDataGeneration;
    console.log('Test function available as window.testMockDataGeneration()');
}

// Run the test
testMockDataGeneration();
